import 'dart:io';

abstract class PlatformInfo {
  String get userGroupId;
  String get sdpAppId;

  factory PlatformInfo() {
    if (Platform.operatingSystem.toLowerCase() == 'macos') {
      return IOSInfo();
    } else {
      return AndroidInfo();
    }
  }
}

class AndroidInfo implements PlatformInfo {
  @override
  String get userGroupId => "6879ff5d745e86001f37de63";

  @override
  String get sdpAppId => "43063c1a-1106-40ae-acce-c756e0e03e7c";
}

class IOSInfo implements PlatformInfo {
  @override
  String get userGroupId => "6879ff5d745e86001f37de63";

  @override
  String get sdpAppId => "43063c1a-1106-40ae-acce-c756e0e03e7c";
}
