<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.aic.earth.research">

    <!-- Permissions -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />

    <!-- Application -->
    <application
        android:allowBackup="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:supportsRtl="true"
        android:theme="@style/AppTheme"
        android:usesCleartextTraffic="true">

        <!-- Sentry configuration -->
        <meta-data android:name="io.sentry.dsn" android:value="https://<EMAIL>/46" />
        <meta-data android:name="io.sentry.traces.user-interaction.enable" android:value="true" />
        <meta-data android:name="io.sentry.attach-screenshot" android:value="true" />
        <meta-data android:name="io.sentry.attach-view-hierarchy" android:value="true" />
        <meta-data android:name="io.sentry.traces.sample-rate" android:value="1.0" />

        <activity
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|locale|smallestScreenSize|screenLayout|uiMode"
            android:name="com.aic.earth.research.MainActivity"
            android:label="@string/title_activity_main"
            android:theme="@style/AppTheme.NoActionBar"
            android:launchMode="singleTask"
            android:screenOrientation="landscape"
            android:exported="true"
            tools:ignore="DiscouragedApi">

            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>

        </activity>
    </application>
</manifest>
