/**
 * 系统全局常量
 */
// 请根据 ENV（npm run xxx --SDP_ENV=yyy 得到的 env 值）来设置对应的key，例如下面的 dev、production
// local 是作为没有提供 ENV 时默认采用
const data = {
  // 本地配置
  local: {
    ONE_ENV: 'test',
    NAME: 'local',
    UPLOAD_CS_HOST: 'betacs.101.com',
    UPLOAD_CS_SERVICE: 'aic_service_scontent',
    CDNCS: 'https://betacs.101.com',
    NDR: {
      ENV: 'preproduction'
    },
    SDP_APP_ID: '93bab922-3169-4e63-8178-e6751475395c',
    TENANT_ID: '1321',
    KLOK_TENANT_ID: '1314',
    CONTAINER_ID: 'a68551a3-99cc-4c83-b3c0-284b9323d3f6',
    AI_COURSEWARE_CONTAINER_ID: 'e478e8d5-4471-4357-9b59-31dd9e52bcee',
    UNREAL_CONTAINER_ID: 'bebab070-5478-4870-a0ac-760c36a3f3a2',
    KLOK_CONTAINER_ID: 'karaoke_courseware',
  },
  // 测试
  test: {
    ONE_ENV: 'product',
    NAME: 'product',
    UPLOAD_CS_HOST: 'cs.101.com',
    UPLOAD_CS_SERVICE: 'aic_service_scontent',
    CDNCS: 'https://cdncs.101.com',
    NDR: {
      ENV: 'product'
    },
    SDP_APP_ID: '8331e61b-f0b0-45fe-8a91-744242662081',
    TENANT_ID: '1321',
    KLOK_TENANT_ID: '1314',
    CONTAINER_ID: 'a68551a3-99cc-4c83-b3c0-284b9323d3f6',
    AI_COURSEWARE_CONTAINER_ID: 'e478e8d5-4471-4357-9b59-31dd9e52bcee',
    UNREAL_CONTAINER_ID: 'bebab070-5478-4870-a0ac-760c36a3f3a2',
    KLOK_CONTAINER_ID: 'karaoke_courseware',
  },
  // 预生产环境
  preproduction: {
    ONE_ENV: 'product',
    NAME: 'product',
    UPLOAD_CS_HOST: 'cs.101.com',
    UPLOAD_CS_SERVICE: 'aic_service_scontent',
    CDNCS: 'https://cdncs.101.com',
    NDR: {
      ENV: 'product'
    },
    SDP_APP_ID: '8331e61b-f0b0-45fe-8a91-744242662081',
    TENANT_ID: '1321',
    KLOK_TENANT_ID: '1314',
    CONTAINER_ID: 'a68551a3-99cc-4c83-b3c0-284b9323d3f6',
    AI_COURSEWARE_CONTAINER_ID: 'e478e8d5-4471-4357-9b59-31dd9e52bcee',
    UNREAL_CONTAINER_ID: 'bebab070-5478-4870-a0ac-760c36a3f3a2',
    KLOK_CONTAINER_ID: 'karaoke_courseware',
  },
  // 生产环境
  product: {
    ONE_ENV: 'product',
    NAME: 'product',
    UPLOAD_CS_HOST: 'cs.101.com',
    UPLOAD_CS_SERVICE: 'aic_service_scontent',
    CDNCS: 'https://cdncs.101.com',
    NDR: {
      ENV: 'product'
    },
    SDP_APP_ID: '8331e61b-f0b0-45fe-8a91-744242662081',
    TENANT_ID: '1321',
    KLOK_TENANT_ID: '1314',
    CONTAINER_ID: 'a68551a3-99cc-4c83-b3c0-284b9323d3f6',
    AI_COURSEWARE_CONTAINER_ID: 'e478e8d5-4471-4357-9b59-31dd9e52bcee',
    UNREAL_CONTAINER_ID: 'bebab070-5478-4870-a0ac-760c36a3f3a2',
    KLOK_CONTAINER_ID: 'karaoke_courseware',
  }
}

// 判断hash是否是resource开头，需要特殊处理下
export const isResource = () => window.location.hash.startsWith('#/resource')

const getEnv = () => {
  const sdpEnv = process.env.SDP_ENV || 'local'
  return sdpEnv
}

// 默认使用 local
const result: typeof data.local = { ...data[getEnv()] }

export default result
