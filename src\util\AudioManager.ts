export default class AudioManager {
  private voiceAudio = new Audio()

  private playbackRate = 1

  private static instances: Map<string, AudioManager> = new Map()

  public static init(key: string = 'default') {
    if (!this.instances.has(key)) {
      this.instances.set(key, new AudioManager())
    }
  }

  public static getInstance(key: string = 'default'): AudioManager {
    if (!this.instances.has(key)) {
      this.instances.set(key, new AudioManager())
    }
    return this.instances.get(key)!
  }

  public setVolume(vol: number = 1) {
    this.voiceAudio.volume = vol
    return this
  }

  public setPlaybackRate(rate: number = 1) {
    this.playbackRate = rate
    return this
  }

  private constructor() {
    // 单例实现，隐藏构造函数
    this.setVolume = this.setVolume.bind(this)
    this.setPlaybackRate = this.setPlaybackRate.bind(this)
    this.play = this.play.bind(this)
    this.stop = this.stop.bind(this)
    this.playUrl = this.playUrl.bind(this)
  }

  /**
   * 播放音效 给定url类型
   */
  private playUrl(url: string, loop = false) {
    try {
      this.voiceAudio.src = url
      this.voiceAudio.playbackRate = this.playbackRate
      this.voiceAudio.loop = loop
      this.voiceAudio?.play()
    } catch (error) {
      console.error('播放音频失败:', error)
    }
  }

  /**
* 获取音频时长
*/
  public getAudioDuration(url) {
    return new Promise((resolve, reject) => {
      const audio = new Audio()
      audio.src = url

      audio.addEventListener('loadedmetadata', () => {
        resolve(audio.duration)
      })

      audio.addEventListener('error', () => {
        reject(new Error('加载失败'))
      })
    })
  }

  /**
   * 停止播放音效
   */
  public stop() {
    this.voiceAudio.pause()
    this.voiceAudio.src = ''
  }

  /**
   * 播放音频并在结束时回调
   * @param url 音频地址
   */
  public play(url: string, loop = false): Promise<boolean> {
    return new Promise((resolve) => {
      this.playUrl(url, loop)
      this.voiceAudio.onended = () => {
        resolve(true)
      }
    })
  }
}
