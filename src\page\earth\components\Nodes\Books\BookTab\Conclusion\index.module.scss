.conclusion {
  height: 100%;
  display: flex;
  .conclusion-last {
    overflow: auto;
    flex: 1;
    height: 100%;
    .conclusion-title-box {
      margin: 2.1875rem 0rem 0rem 1.875rem;
      display: flex;
      .conclusion-title {
        font-family: "Alibaba PuHuiTi";
        font-size: 1.5rem;
        font-style: normal;
        font-weight: 700;
        color: #655c5c;
        margin-right: 2.75rem;
        position: relative;
        text-shadow: 0 0.1875rem 0 #fff;
        .conclusion-key {
          position: absolute;
          left: 0rem;
          top: 2.1875rem;
          width: 5.625rem;
        }
      }
      .conclusion-des {
        flex: 1;
        font-size: 0.875rem;
        font-style: normal;
        font-weight: 400;
        position: relative;
        img {
          position: absolute;
          top: 1.375rem;
          right: 2.5rem;
          width: 100%;
        }
      }
    }
    .conclusion-content {
      padding: 1.25rem;
      display: flex;
      flex-direction: column;
      height: calc(100% - 4.5rem);
    }
  }
  .conclusion-form {
    flex: 1;
    padding: 0.625rem;
    height: 100%;
    display: flex;
    flex-direction: column;
    .conclusion-title-box {
      margin: 1.5625rem 0rem 0rem 1.25rem;
      display: flex;
      margin-bottom: 1.25rem;
      .conclusion-title {
        font-family: "Alibaba PuHuiTi";
        font-size: 1.5rem;
        font-style: normal;
        font-weight: 700;
        color: #655c5c;
        margin-right: 2.75rem;
        position: relative;
        text-shadow: 0 0.1875rem 0 #fff;
        .conclusion-key {
          position: absolute;
          left: 0rem;
          top: 2.1875rem;
          width: 5.625rem;
        }
      }
      .conclusion-des {
        flex: 1;
        font-size: 0.875rem;
        font-style: normal;
        font-weight: 400;
        position: relative;
        img {
          position: absolute;
          top: 1.375rem;
          right: 2.5rem;
          width: 100%;
        }
      }
    }
    .conclusion-from-content {
      flex: 1;
      padding: 0.625rem 0rem 0.625rem 1.875rem;
      background: url("../../../../../../../asset/images/books/experiment/reflection_line.png")
        no-repeat center center / cover;
      .form-title {
        background: url("../../../../../../../asset/images/books/conclusion/conclusion_title1.png")
          no-repeat center center / cover;
        font-size: 1rem;
        height: 2.125rem;
        line-height: 2.125rem;
        padding-left: 2.5rem;
        color: #fff;
        margin-bottom: 0.625rem;
        &.title2 {
          background: url("../../../../../../../asset/images/books/conclusion/conclusion_title2.png")
            no-repeat center center / cover;
          margin-top: 0.625rem;
        }
      }
      .block-label {
        color: #847674;
        font-family: "Alibaba PuHuiTi";
        font-size: 1rem;
        font-style: normal;
        font-weight: 500;
        line-height: 2rem;
      }
      .block-des {
        position: relative;
        height: 12.5rem;
        overflow: auto;

        .earth-icon {
          position: absolute;
          top: 0rem;
          right: 6.25rem;
          width: 5.625rem;
        }
      }
      .block-value {
        color: #be8d56;
      }
      .block-memo {
        position: relative;
        width: 100%;
        height: 11.25rem;
        margin: 0.625rem 0rem;
        background: url("../../../../../../../asset/images/books/experiment/reflection_input_bg.png")
          no-repeat center center;
        background-size: 100% 100%;
        .w-icon {
          position: absolute;
          bottom: 0.3125rem;
          right: 0.625rem;
          width: 2.5rem;
        }
        :global {
          .fish-input-textarea-container {
            position: absolute;
            width: calc(100% - 2.5rem);
            left: 1.25rem;
            top: 1.25rem;
            .fish-input {
              background: transparent;
              border: unset;
              box-shadow: unset;
              color: #664b48;
              font-size: 1rem;
            }
          }
        }
      }
    }
  }
}
