import React, { useEffect, useState } from 'react'
import classNames from 'classnames/bind'
import { Modal } from 'fish'
import EventBus from '@/component/EventBus'
import { Events } from '@/component/EventBus/constant'
import { useLatest } from 'ahooks'
import { useAppDispatch } from '@/store'
import { updateData } from '@/page/earth/earthSlice'
import bookBtnPng from '@/asset/images/books/book_button.png'
import AudioManager from '@/util/AudioManager'
import styles from './index.module.scss'
import Books, { BOOK_TABS } from './Books'
import { BOOK_GUIDE_MAP } from './constant'

const cn = classNames.bind(styles)

const BooksButton = ({ guided, onGuided }) => {
  const [visible, setVisible] = useState(false)
  const lastVisible = useLatest(visible)
  const [currentTab, setCurrentTab] = useState('hypothesis')
  const dispatch = useAppDispatch()

  const manualOpen = () => {
    if (guided) {
      onGuided()
      return
    }
    setVisible(true)
    dispatch(updateData({ manualBook: true }))
  }

  useEffect(() => {
    const onOpen = (_data) => {
      if (_data.name === 'book' && (lastVisible.current === false)) {
        console.log('onOpen book')
        setVisible(true)
        dispatch(updateData({ manualBook: false }))
        if (_data.tab) {
          const tab = (BOOK_TABS[_data.tab] || BOOK_TABS[0]).key
          setCurrentTab(tab)
        }
        if (_data.tab === '0') {
          if (_data.id === 'node_7') {
            EventBus.emit(Events.aiGuide, BOOK_GUIDE_MAP.question1, () => {
              EventBus.emit(Events.aiGuide, BOOK_GUIDE_MAP.question2, () => {
                EventBus.emit(Events.aiGuide, BOOK_GUIDE_MAP.question3,)
              })
            })
          }
          if (_data.id === 'node_9') {
            EventBus.emit(Events.aiGuide, BOOK_GUIDE_MAP.presume1, () => {
              EventBus.emit(Events.aiGuide, BOOK_GUIDE_MAP.presume2, () => {
                EventBus.emit(Events.aiGuide, BOOK_GUIDE_MAP.presume3)
              })
            })
          }
        } else if (_data.tab === '3') {
          EventBus.emit(Events.aiGuide, BOOK_GUIDE_MAP.tab4)
        }
      }
    }
    const onClose = (name) => {
      if (name === 'book' && (lastVisible.current === true)) {
        setVisible(false)
        AudioManager.getInstance('sfx').stop()
      }
    }

    EventBus.on(Events.OpenComponent, onOpen)
    EventBus.on(Events.CloseComponent, onClose)

    return () => {
      EventBus.off(Events.OpenComponent, onOpen)
      EventBus.off(Events.CloseComponent, onClose)
    }
  }, [])

  const onHide = () => {
    AudioManager.getInstance('sfx').stop()
    setVisible(false)
  }

  return (
    <div className={cn('books-button', { guided })}>
      <img src={bookBtnPng} className={cn('btn')} onClick={manualOpen} id="book-btn" />
      <Modal
        visible={visible}
        footer={null}
        title={null}
        wrapClassName={cn('books-modal')}
        style={{ top: '7rem' }}
        bodyStyle={{ padding: 0 }}
        getContainer={false}
        onCancel={onHide}
        destroyOnClose
      >
        <Books defaultTab={currentTab} />
      </Modal>
    </div>
  )
}

export default BooksButton
