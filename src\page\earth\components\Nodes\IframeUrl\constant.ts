import music_success from '@/asset/musics/simulate/success.wav'
import music_fail_4 from '@/asset/musics/simulate/fail_4.wav'
import music_fail_5 from '@/asset/musics/simulate/fail_5.wav'
import music_fail_7 from '@/asset/musics/simulate/fail_7.wav'
import music_fail_9 from '@/asset/musics/simulate/fail_9.wav'
import music_fail_10 from '@/asset/musics/simulate/fail_10.wav'

import music_earthquake_1 from '@/asset/musics/earthquake/1.wav'
import music_earthquake_2 from '@/asset/musics/earthquake/2.wav'
import music_earthquake_3 from '@/asset/musics/earthquake/3.wav'
import music_earthquake_4 from '@/asset/musics/earthquake/4.wav'
import music_earthquake_5 from '@/asset/musics/earthquake/5.wav'

import volcanoDesMusic from '@/asset/musics/des/volcano_des.wav'
import mountainDesMusic from '@/asset/musics/des/mountain_des.wav'
import experimentDes1Music from '@/asset/musics/des/experiment_des1.wav'
import experimentDes2Music from '@/asset/musics/des/experiment_des2.wav'
import experimentDes3Music from '@/asset/musics/des/experiment_des3.wav'

/* eslint-disable max-len */
export const SIMULATE_RESULT_MAP = {
  fail_4: '地球表面无板块运动，爆发全球性爆炸',
  fail_5: '火山休眠、板块运动不活跃，地表被超厚岩石层覆盖，形成一片固态岩石平原',
  success: '剧烈火山喷发、板块间互相撞击形成高山',
  fail_7: '实验结果：混合大量岩石的岩浆海',
  fail_9: '参数配置冲突',
  fail_10: '参数配置冲突~',
}

export const SIMULATE_NPC_MAP = {
  fail_4: {
    dialogue: '哇，很有视觉冲击力，但是此次模拟未成功哦。我看到地表出现了全球性的爆炸，非常震撼。我想我们需要再模拟一次，在此之前请你打开探究笔记，记录下这次模拟得到的经验。',
    music: music_fail_4
  },
  fail_5: {
    dialogue: '看起来和真实的地球情况不太像，模拟未能成功。我发现火山都休眠了，地表被超厚岩石层覆盖，形成一片固态岩石平原。用探究笔记记录下这次的实验结果和经验，之后我们再来一次！',
    music: music_fail_5
  },
  success: {
    dialogue: '恭喜你成功地模拟出了地球上存在的火山喷发，板块撞击带来的造山运动等现象。如你的参数所设置，地球内部可分为三层结构，最外层是低温脆性岩石材质，我们称之为地壳。地壳处于持续运动中，板块之间的碰撞、挤压或张裂，通过岩浆活动、变质作用和沉积过程，形成了地壳中的三大类岩石：岩浆岩、沉积岩和变质岩。而地球的中间层，超高温高压下的固态塑性岩石材质，我们称之为地幔。最内层的超高温铁镍合金，就是地核了。',
    music: music_success
  },
  fail_7: {
    dialogue: '哦，我想人类没办法站在这样一个燃烧着的星球表面，但不得不说，这个岩浆海令人印象深刻。让我们在探究笔记中记录下这次实验的心得吧。然后，我们得再模拟一次！',
    music: music_fail_7
  },
  fail_9: {
    dialogue: '在重力作用下，铁镍等较重的物质会下沉，而铝硅等较轻的物质会往上浮，因此按照当前材质顺序是无法构成一个稳定的星球的',
    music: music_fail_9
  },
  fail_10: {
    dialogue: '地球深处的能量不断积聚，最终会在表层释放，因此地表是不可能永远无运动的哦~',
    music: music_fail_10
  }
}
export const MATERIAL_MAP = {
  material1: '低温脆性岩石材质',
  material2: '超高温高压下的固态塑性岩石材质',
  material3: '超高温致密金属材质'
}

export const EARTHQUAKE_MAP = {
  onEarthquakeStartChat: {
    dialogue: '现在按下地震波检测设备的红色按钮，启动地震波收集。',
    music: music_earthquake_1
  },
  onEarthquakeCollectWaveData: {
    dialogue: '好的，已经收集到数据。',
    music: music_earthquake_2
  },
  onEarthquakeShowChart: {
    dialogue: '这张图是基于大量地震观测数据，综合分析地震波在地球内部不同深度的传播速度规律后，汇总绘制的地球内部结构与地震波速度关系的通用模型图。',
    music: music_earthquake_3
  },
  onEarthquakeShowStartCollect: {
    dialogue: '图上提供了地震波传播的深度、速度等数据。',
    music: music_earthquake_4
  },
  onEarthquakePressStartCollect: {
    dialogue: '请观察模型图，是否能发现线索！',
    music: music_earthquake_5
  }
}

export const ThreeDes = {
  volcano: {
    music: volcanoDesMusic,
    dialogue: '看看这座火山，猜猜地下隐藏着什么能量密码？它和地球的内部结构有什么联系？',
  },
  mountain: {
    music: mountainDesMusic,
    dialogue: '请观察山体的纹路，是什么神秘力量把海底推成了高山？地球的表面在运动还是静止的？',
  },
  experiment1: {
    music: experimentDes1Music,
    dialogue: '点击模拟实验按钮，调整参数验证你的假设吧！',
  },
  experiment2: {
    music: experimentDes2Music,
    dialogue: '若结果不符预期，大胆优化假设。',
  },
  experiment3: {
    music: experimentDes3Music,
    dialogue: '每一次的模拟，都离真相更进一步。',
  }
}
