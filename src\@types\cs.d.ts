declare namespace NsCS {
  interface ICSUploadResponse {
    create_at: number
    dentry_id: string
    expire_at: number
    flag: number
    folder: false
    hits: 0
    inode_id: string
    name: string
    parent_id: string
    path: string
    scope: number
    serviceName: string
    service_id: string
    type: number
    uid: number
    update_at: number
    [x: string]: unknown
  }

  interface ICSProgressResponse {
    loaded: number
    [x: string]: unknown
  }

  type TSuccessEventCallback = (resp: { data: ICSUploadResponse }) => unknown

  type TProgressEventResp = (resp: ICSProgressResponse) => unknown

  type TMd5ProgressEventCallback = (resp: ICSProgressResponse) => unknown

  type TErrorEventCallback = (error: Error) => unknown

  /**
   * 上传成功时触发
   */
  type TOnSuccessEvent = (event: 'success', cb: TSuccessEventCallback) => void
  /**
   * 返回项目上传进度
   */
  type TOnProgressEvent = (event: 'progress', cb: TProgressEventResp) => void
  /**
   * 返回项目进度
   */
  type TOnMd5Progress = (event: 'md5Progress', cb: TMd5ProgressEventCallback) => void
  /**
   * 事件中止时触发，‘停止’也会触发此事件
   */
  type TOnErrorEvent = (event: 'error', cb: TErrorEventCallback) => void

  /**
   * 开始上传，若已上传成功则不再上传
   */
  type TEmitStart = (event: 'start') => void

  /**
   * 暂停上传，支持断点续传 此方法并不会立即暂停上传,实际状态由 emitter.state 为主
   */
  type TEmitPause = (event: 'pause') => void

  /**
   * 停止上传，并且不会断点续传 此方法并不会立即停止上传,实际状态由 emitter.state 为主 旁路分块上传若中途中止，会导致无法重新上传。
   */
  type TEmitStop = (event: 'stop') => void

  const enum UploadState {
    NotStart,
    Uploading,
    Success,
    Fail,
    Paused
  }

  interface Emitter {
    /**
     * 上传任务的当前状态
     * 0 '未开始'
     * 1 '上传中'
     * 2 '成功'
     * 3 '失败'
     * 4 '暂停'
     */
    state: UploadState
    /**
     * 上传文件名
     */
    name: string
    on: TOnSuccessEvent & TOnProgressEvent & TOnMd5Progress & TOnErrorEvent
    emit: TEmitStart & TEmitPause & TEmitStop
  }

  interface IUploadOptions {
    /**
     * 服务器对应的地址如 '/folder'
     */
    dirName: string
    /**
     * 文件名称，默认file.name 含后缀名
     */
    name?: string
    /**
     * 是否覆盖服务端文件 默认：false
     */
    isCover?: boolean
    /**
     * 仅覆盖上传时有效，若传入dentryId,则会覆盖path
     */
    dentryId?: string
    /**
     * 是否公开文件 0-私密 1-公开 默认：0
     */
    scope?: 0 | 1
    /**
     * 过期天数 0-永不过期 默认：0
     */
    expireDays?: number
    /**
     * 备注名
     */
    otherName?: string
    /**
     * JSON格式的string 自定义元数据
     */
    infoJson?: string
    /**
     * JSON格式的string 额外元数据
     */
    metaJson?: string
    /**
     * 上传到指定区域的存储节点上 混合部署专用
     */
    regionName?: string
    /**
     * 上传到指定存储节点上 混合部署专用
     */
    bucketName?: string
    /**
     * 任意值，仅作为额外参数，在调用getToken方法时作为额外自定义参数传入
     */
    extraInfo?: unknown
    /**
     * 最大分块并发数, 默认为5, 最大不可超过8（注：并发数受浏览器限制，比如chrome限制一个域名同时请求数为6个，超过则pending）
     */
    maxChunkRequests?: number
  }

  interface CsInstance {
    /**
     * 上传文件
     * @param {file} file - h5的file文件类型
     * @param {Object} [option] - 额外配置项
     * @return {emitter} emitter - event实例
     */
    upload: (file: File, options?: IUploadOptions) => Promise<Emitter>

    /**
     * 下载文件
     * @return {Array} 由于云端会根据当前IP智能调度可用的域名，故可能会返回多个url，最优的靠前，正常取第一个即可
     */
    getDownloadURL: (params: {
      /**
       * 下载的文件路径
       */
      path?: string
      /**
       * path dentryId 二选一
       */
      dentryId?: string
      /**
       * 文件是否为公开文件 0-私密 1-公开, 默认 0
       */
      scope?: 0 | 1
      /**
       * 缩略图短边尺寸（像素）
       */
      size?: 80 | 120 | 160 | 240 | 320 | 480 | 640 | 960 | 1080 | 1200
      /**
       * 生成缩略图的格式
       */
      ext?: 'jpg' | 'png' | 'jpeg' | 'bmp' | 'gif' | 'webp'
      /**
       * 是否强制浏览器进行附件下载, 默认为false
       */
      attachment?: boolean
      /**
       * 用于强制浏览器下载附件后，指定下载下来的文件名
       */
      name?: string
      /**
       * 任意值，仅作为额外参数，在调用getToken方法时作为额外自定义参数传入
       */
      extraInfo?: unknown
    }) => Promise<string[]>

    /**
     * 获取公开文件下载地址
     * @return {Array} 由于云端会根据当前IP智能调度可用的域名，故可能会返回多个url，最优的靠前，正常取第一个即可
     */
    getPublicDownloadURL: (params: {
      /**
       * 下载的文件路径
       */
      path?: string
      /**
       * path dentryId 二选一
       */
      dentryId?: string
      /**
       * 缩略图短边尺寸（像素）
       */
      size?: 80 | 120 | 160 | 240 | 320 | 480 | 640 | 960 | 1080 | 1200
      /**
       * 生成缩略图的格式
       */
      ext?: 'jpg' | 'png' | 'jpeg' | 'bmp' | 'gif' | 'webp'
      /**
       * 是否强制浏览器进行附件下载, 默认为false
       */
      attachment?: boolean
      /**
       * 用于强制浏览器下载附件后，指定下载下来的文件名
       */
      name?: string
    }) => Promise<string[]>

    /**
     * 文件删除 path和dentryId同时存在，则优先使用path
     * @param {string}  path - 文件路径
     * @param {string}  dentryId - path dentryId 二选一
     * @param  {Any}    [extraInfo] - 任意值，仅作为额外参数，在调用getToken方法时作为额外自定义参数传入
     */
    deleteCSEntity: (params: {
      path?: string
      dentryId?: string
      extraInfo?: unknown
    }) => Promise<void>
  }

  interface ITokenInfo {
    dateTime: string
    policy: string
    stringToSign: string
    token: string
    dateOrExpireAt: string
    expireAt?: string
    expire_at?: string
  }

  interface ITokenRequestBody {
    token_entity: {
      token_type: string
      path: string
      dentry_id: string
      params: string
    }
    service_name: string
  }

  type TGetToken = (params: any) => Promise<ITokenInfo>
}

declare module '@sdp.nd/js-cs-sdk' {
  interface SDKParams {
    serviceName: string
    host: string
    protocol: string
    getToken: NsCS.TGetToken
  }

  function CsSDK(params: SDKParams): Promise<NsCS.CsInstance>

  export default CsSDK
}
