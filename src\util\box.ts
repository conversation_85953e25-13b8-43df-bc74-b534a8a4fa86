/**
 * 获取元素中心点在当前页面的坐标位置
 * @param element - DOM 元素
 * @returns 包含 x, y 坐标的对象（元素中心点）
 */
export function getElementPosition(element: HTMLElement): { x: number; y: number } {
  const rect = element.getBoundingClientRect()

  // 获取页面滚动偏移量
  const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop

  // 计算中心点坐标
  return {
    x: rect.left + scrollLeft + rect.width / 2,
    y: rect.top + scrollTop + rect.height / 2
  }
}

/**
 * 获取元素中心点相对于视口的坐标位置
 * @param element - DOM 元素
 * @returns 包含 x, y 坐标的对象（元素中心点）
 */
export function getElementViewportPosition(element: HTMLElement): { x: number; y: number } {
  const rect = element.getBoundingClientRect()

  // 计算中心点坐标
  return {
    x: rect.left + rect.width / 2,
    y: rect.top + rect.height / 2
  }
}
