.saves {
  height: 100vh;
  background: url("../../../../asset/images/home/<USER>") no-repeat center center;
  background-size: 100% 100%;
  width: 100vw;
  display: flex;

  .saves-list {
    flex: 1;
    padding: 4.375rem 0rem 0rem 6.25rem;
    .list-header {
      img {
        width: 100%;
      }
    }
    .list-content {
      margin-top: 1.25rem;
      height: calc(100% - 4.375rem);
      overflow: auto;
      .save-item {
        width: 100%;
        margin-bottom: 0.75rem;
        padding: 0.625rem;
        color: #fff;
        background: url("../../../../asset/images/saves/save_item_default.png") no-repeat center
          center;
        background-size: 100% 100%;
        display: flex;
        justify-content: space-between;
        height: 6.5rem;
        .save-info {
          display: flex;
          font-size: 1rem;
          line-height: 2rem;
          .save-name {
            padding-right: 0.9375rem;
            border-right: 0.125rem solid rgba(255, 255, 255, 0.3);
            margin-right: 0.9375rem;
          }
        }
        .save-actions {
          img {
            position: relative;
            bottom: -2.5rem;
            width: 5.0625rem;
          }
        }
        &.used {
          background: url("../../../../asset/images/saves/save_item_used.png") no-repeat center
            center;
          background-size: 100% 100%;
        }
      }
    }
  }

  .save-tip {
    width: 42%;
    position: relative;
    .save-tip-image {
      max-width: 100%;
      height: auto;
      position: absolute;
      z-index: 1;
      bottom: 0;
      right: 0px;
    }
    .save-start {
      position: absolute;
      z-index: 2;
      right: 2.1875rem;
      bottom: 2.1875rem;
      display: flex;
      align-items: center;
      .start-text {
        width: 21.25rem;
      }
      .start-btn {
        width: 9rem;
      }
    }
  }
}

.full-image {
  position: fixed;
  top: 11.25rem;
  left: 50%;
  width: 32.5rem;
  transform: translateX(-50%);
  z-index: 999;
  display: none;

  &.showFull {
    display: block;
  }
}

.delete-confirm {
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  position: fixed;
  z-index: 2;
  display: none;
  &.visible {
    display: flex;
    flex-direction: column;
  }
  .delete-box {
    padding: 0.625rem;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 29.375rem;
    height: 13.75rem;
    background: url("../../../../asset/images/saves/confirm_bg.png") no-repeat center center;
    background-size: 100% 100%;
    position: absolute;
    display: flex;
    flex-direction: column;
    .header {
      color: #fff;
      font-style: normal;
      font-weight: 700;
      text-shadow: -1px -1px 0 #000, 1px -1px 0 #000, -1px 1px 0 #000, 1px 1px 0 #000;
      position: relative;
      display: flex;
      justify-content: space-between;
      img {
        width: 1.5rem;
        height: 1.5rem;
      }
    }
    .content {
      flex: 1;
      background-color: #fff;
      border-radius: 0.625rem;
      margin-top: 0.625rem;
      width: 100%;
      .text {
        margin: 2.5rem;
      }
      .buttons {
        display: flex;
        justify-content: center;
        margin-top: 0.625rem;
        img {
          width: 9.375rem;
          margin: 0 0.625rem;
          cursor: pointer;
        }
      }
    }
  }
}

// 854x466 屏幕
@media (max-width: 854px) and (max-height: 466px) {
  .save-tip-image {
    max-height: 90% !important;
  }
}
