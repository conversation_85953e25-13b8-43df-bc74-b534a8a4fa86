import React, {
  FC, useMemo,
} from 'react'
import classNames from 'classnames/bind'
import { Col, Input, Row } from 'fish'
import sourceHeaderBgPng from '@/asset/images/books/hypothesis/source_imgs/source_header_bg.png'
import experimentalResult from '@/asset/images/books/experiment/experimental_result.png'
import experimentalConclusion from '@/asset/images/books/conclusion/experimental_conclusion.png'
import inputIcon from '@/asset/images/books/experiment/reflection_input_icon.png'
import earthIcon from '@/asset/images/books/conclusion/earth.png'
import { IEarthData, UPGRADE_KEY } from '@/page/earth/interface'
import _ from 'lodash'
import { useUpdateMedals } from '@/page/earth/hooks/useAchievement'
import styles from './index.module.scss'
import { ExperimentItem } from '../Experiment/Experiment'
import { LABEL_MAP } from '../../constant'
import { MedalKey } from '../../../Achievement/constant'

const cn = classNames.bind(styles)

interface IConclusionProps {
  data: IEarthData['books']
  onChange: (data) => void
}

const Conclusion: FC<IConclusionProps> = ({
  data,
  onChange
}) => {
  const { updateGrade } = useUpdateMedals()
  const { lastSuccess, presume } = useMemo(() => {
    const { experiment, hypothesis } = data
    return {
      lastSuccess: _.findLast(experiment || [], (item) => item.success),
      presume: _.last(hypothesis.presumes || [])
    }
  }, [data])
  const _onChange = (newData) => {
    onChange(newData)
    updateGrade(MedalKey.earth_researcher, { [UPGRADE_KEY.conclusion]: true })
  }
  return (
    <div className={cn('conclusion')}>
      <div className={cn('conclusion-last')}>
        <div className={cn('conclusion-title-box')}>
          <span className={cn('conclusion-title')}>
            实验结果
            <img className={cn('conclusion-key')} src={experimentalResult} />
          </span>
          <span className={cn('conclusion-des')}>
            <img src={sourceHeaderBgPng} />
          </span>
        </div>
        <div className={cn('conclusion-content')}>
          <ExperimentItem data={lastSuccess} />
        </div>
      </div>
      <div className={cn('conclusion-form')}>
        <div className={cn('conclusion-title-box')}>
          <span className={cn('conclusion-title')}>
            实验结论
            <img className={cn('conclusion-key')} src={experimentalConclusion} />
          </span>
          <span className={cn('conclusion-des')}>
            <img src={sourceHeaderBgPng} />
          </span>
        </div>
        <div className={cn('conclusion-from-content')}>
          <div className={cn('form-title')}>我的假设</div>
          <div className={cn('block-des')}>
            <div className={cn('block-label')}>
              地球内部存在三层结构，其特点如下：
              {
                Object.keys(LABEL_MAP).map((key) => (
                  <Row key={key}>
                    <Col span={4}>
                      {LABEL_MAP[key]}
                    </Col>
                    <Col span={20} className={cn('block-value')}>
                      {presume?.[key]?.value}
                    </Col>
                  </Row>
                ))
              }
            </div>
            <img src={earthIcon} className={cn('earth-icon')} />
          </div>
          <div className={cn('form-title', 'title2')}>我的解说词</div>
          <div className={cn('block-label')}>
            大家好，欢迎来到地质博物馆，我是今天的志愿讲解员，给大家讲解地球的内部结构和地壳特征。
          </div>
          <div className={cn('block-memo')} id="block-memo">
            <Input.TextArea
              rows={4}
              defaultValue={data?.conclusion?.conclusion_value}
              key={data?.conclusion?.conclusion_value}
            />
            <img
              src={inputIcon}
              className={cn('w-icon')}
              onClick={(e) => {
                e.stopPropagation()
                const inputElement = e.currentTarget.closest('#block-memo')?.querySelector('textarea')
                const currentValue = inputElement?.value || ''
                _onChange({ conclusion: { conclusion_value: currentValue } })
              }}
            />
          </div>
          <div className={cn('block-label')}>
            我们的地球很神奇吧？欢迎您继续参观，还有更多的神奇等待各位的探索和发现！
          </div>
        </div>
      </div>
    </div>
  )
}

export default Conclusion
