import { useMemo } from 'react'
import { useAppDispatch, useAppSelector } from '@/store'
import { SCORE_LIST, MEDAL_LIST, MedalKey } from '../components/Nodes/Achievement/constant'
import { updateData } from '../earthSlice'

export interface ScoreItem {
  name: string
  key: string
  count: number
  description: string
}

export interface MedalItem {
  name: string
  key: MedalKey
  default_icon: string
  dis_icon: string
  description: string
  unlocked: boolean
  upgrade?: { [key: string]: boolean }
}

export interface AchievementData {
  medals: MedalItem[]
  scores: ScoreItem[]
  totalScore: number
  unlockedMedalCount: number
  progress: {
    clueCount: number
    questionCount: number
    questionPresumesCount: number
    presumesCount: number
    experimentCount: number
  }
}

/**
 * 获取成就数据的自定义Hook
 * @returns {AchievementData} 成就相关的所有数据
 */
export const useAchievement = (): AchievementData => {
  const { data } = useAppSelector((state) => state.earth)
  const { achievement: { medals }, books, config: { aiMode } } = data

  // 计算各类积分数量
  const progress = useMemo(() => {
    const clueCount = books.hypothesis.clue.length
    const questionCount = books.hypothesis.question_presumes.filter((q) => q.question).length
    const questionPresumesCount = books.hypothesis.question_presumes.filter((q) => q.presume).length
    const presumesCount = books.hypothesis.presumes.length
    const experimentCount = books.experiment.length

    return {
      clueCount,
      questionCount,
      questionPresumesCount,
      presumesCount,
      experimentCount
    }
  }, [books])

  // 计算积分详情
  const scores = useMemo(() => SCORE_LIST.map((item) => {
    let count = 0
    switch (item.key) {
      case 'clue':
        count = progress.clueCount
        break
      case 'question':
        count = progress.questionCount
        break
      case 'question_presumes':
        count = progress.questionPresumesCount
        break
      case 'presumes':
        count = progress.presumesCount
        break
      case 'experiment':
        count = progress.experimentCount
        break
      default:
        count = 0
    }
    return {
      ...item,
      count,
    }
  }), [progress])

  // 计算总积分
  const totalScore = useMemo(() => scores.reduce((sum, item) => sum + item.count, 0), [scores])

  // 合并勋章数据  AI模式关闭时，隐藏AI先锋勋章
  const medalData = useMemo(() => MEDAL_LIST.map((medalTemplate) => {
    const userMedal = medals.find((m) => m.key === medalTemplate.key)
    return {
      ...medalTemplate,
      ...userMedal,
      unlocked: userMedal?.unlocked ?? false,
    }
  }).filter((i) => aiMode || i.key !== MedalKey.communication_pioneer), [medals, aiMode])

  // 计算已解锁勋章数量
  const unlockedMedalCount = useMemo(() => medalData.filter((medal) => medal.unlocked).length, [medalData])

  return {
    medals: medalData,
    scores,
    totalScore,
    unlockedMedalCount,
    progress
  }
}

/**
 * 检查特定勋章是否已解锁
 * @param medalKey 勋章key
 * @returns {boolean} 是否已解锁
 */
export const useMedalUnlocked = (medalKey: MedalKey): boolean => {
  const { medals } = useAchievement()
  return medals.find((medal) => medal.key === medalKey)?.unlocked || false
}

/**
 * 获取特定类型的积分数量
 * @param scoreKey 积分类型key
 * @returns {number} 积分数量
 */
export const useScoreCount = (scoreKey: string): number => {
  const { scores } = useAchievement()
  return scores.find((score) => score.key === scoreKey)?.count || 0
}

/**
 * 获取AI相关统计数据
 * @returns AI对话和唤起统计
 */
export const useAIStats = () => {
  const { data } = useAppSelector((state) => state.earth)
  const { ai } = data

  return {
    totalCount: ai.count, // 总唤起次数
    activeCount: ai.activeCount, // 主动唤起次数
    messageCount: ai.messages.length, // 消息总数
    loading: ai.loading, // 是否正在加载
    error: ai.error // 错误信息
  }
}

/**
 * 解锁成就
 * @returns {Object} 研究进度数据
 */
export const useUnLockMedals = () => {
  const dispatch = useAppDispatch()
  const { progress } = useAchievement()

  const { data } = useAppSelector((state) => state.earth)
  const { achievement: { medals }, ai } = data
  // 获取成就里每个key的值
  const achievementKeys = medals.reduce((acc, item) => {
    acc[item.key] = item.unlocked
    return acc
  }, {} as unknown as Record<string, boolean>)

  const canUnlockMedal = (medalKey: MedalKey): boolean => {
    const medal = medals.find((m) => m.key === medalKey)

    // 如果已经解锁，返回true
    if (medal?.unlocked) return true
    // 地球勋章额外处理
    switch (medalKey) {
      case MedalKey.curious_rocket:
        return progress.questionCount > 3
      case MedalKey.explorer:
        return progress.questionPresumesCount > 3
      case MedalKey.deep_explorer:
        return progress.presumesCount > 3
      case MedalKey.perseverance:
        return progress.experimentCount >= 3
      case MedalKey.communication_pioneer:
        return ai.activeCount > 3
      default:
        return false
    }
  }

  const unlockMedal = (medalKey: MedalKey) => {
    if (canUnlockMedal(medalKey) && !achievementKeys[medalKey]) {
      dispatch(updateData({
        achievement: {
          ...data.achievement,
          medals: medals.map((item) => (item.key === medalKey ? {
            ...item,
            unlocked: true,
            time: new Date().getTime()
          } : item))
        }
      }))
    }
  }
  return { unlockMedal }
}

// 更新勋章
export const useUpdateMedals = () => {
  const dispatch = useAppDispatch()
  const { data } = useAppSelector((state) => state.earth)

  const updateGrade = (medalKey: MedalKey, upgrade: Record<string, boolean>) => {
    // 判断是否需要更新勋章
    const medal = data.achievement.medals.find((m) => m.key === medalKey)
    if (!medal) return
    // 如果传入的勋章之前已经未true了，则不再更新
    if (medal.unlocked && Object.keys(upgrade).every((key) => medal.upgrade?.[key])) {
      return
    }

    dispatch(updateData({
      achievement: {
        ...data.achievement,
        medals: data.achievement.medals.map((item) => (item.key === medalKey ? {
          ...item,
          time: new Date().getTime(),
          unlocked: true,
          upgrade: {
            ...item.upgrade,
            ...upgrade
          }
        } : item))
      }
    }))
  }

  return { updateGrade }
}
