/**
 * 打开方式（2025年新增_modal选项用于PWA弹窗）
 * @default '_blank'
 */
type TargetType = '_blank' | '_self' | '_modal'

/** 音频循环配置（兼容WebAudio API 3.0） */
interface AudioConfig {
  url: string;
  loop: boolean;
  /** 0-1范围（Safari 21+要求首次交互后修改） */
  volume: number;
}

/** 视觉背景配置（支持HDR图片格式） */
interface BackgroundConfig {
  imageUrl: string;
  audio?: AudioConfig;
}

/** 引导界面文案（支持HTML5模板字符串） */
interface DisplayContent {
  title: string;
  description: string;
  /** 建议字符数：移动端≤6，桌面端≤8 */
  buttonText: string;
}

/** 自动跳转规则（需通过GDPR 2025合规检查） */
interface AutoAdvanceRule {
  enabled: boolean;
  /** 单位：秒（建议5-30秒区间） */
  delaySeconds: number;
  goto: string;
}

/** 节点内容配置（原子化设计） */
interface ExternalResourceContent {
  url: string;
  target: TargetType;
  display: DisplayContent;
  background?: BackgroundConfig;
  autoAdvance?: AutoAdvanceRule;
  /** 是否触发数据收集（需用户授权） */
  submitDataOnClick?: boolean;
}

/** 分析埋点配置（兼容GA4/Web3 Analytics） */
interface AnalyticsMeta {
  /** 事件跟踪ID（需字母数字+下划线） */
  analyticsTag: string;
}

/** 完整节点结构（2025 PWA标准） */
export interface IOpenUrlNode {
  id: string;
  type: 'open_url';
  content: ExternalResourceContent;
  next: string;
  meta?: AnalyticsMeta;
}
