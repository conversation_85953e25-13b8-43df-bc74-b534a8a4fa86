import React, { useEffect, useRef, useState } from 'react'
import classNames from 'classnames/bind'
import { useAppDispatch, useAppSelector } from '@/store'
import { Progress } from 'fish'
import _ from 'lodash'
import startButton from '@/asset/images/home/<USER>'
import { DataTracker } from '@/sdk/Tracker'
import styles from './index.module.scss'
import { setNodeInfo, STEP, updateConfig } from '../../earthSlice'
import { NODES_REQUEST_URL } from '../../constant'
import usePreloader from '../../hooks/usePreload'
import { IEarthConfigInfo } from '../../interface'

const cn = classNames.bind(styles)

const Home = () => {
  const { nodeInfo, data } = useAppSelector((state) => state.earth)
  const { guided } = data

  const { resourcePreloader } = usePreloader()
  const dispatch = useAppDispatch()
  const [percent, setPercent] = useState(20) // 初始进度20%
  // 用于跟踪完成状态的标志
  const completedTasks = useRef({
    resources: false,
    sdk: false
  })

  const onStart = () => {
    dispatch(updateConfig({ step: STEP.SAVES }))
  }

  // 更新进度的函数
  const updateProgress = (taskType: 'resources' | 'sdk', progress?: number) => {
    if (taskType === 'resources') {
      // 资源加载完成
      if (progress === 1) {
        completedTasks.current.resources = true
      }
      const newPercent = 20 + Math.ceil(progress! * 70) + (completedTasks.current.sdk ? 10 : 0)
      setPercent(newPercent) // 20% + 70% + (SDK完成?10%:0%)
    } else if (taskType === 'sdk') {
      // SDK初始化完成
      completedTasks.current.sdk = true
      const newPercent = 20 + (completedTasks.current.resources ? 70 : 0) + 10
      setPercent(newPercent) // 20% + (资源完成?70%:0%) + 10%
    }
  }

  useEffect(() => {
    setPercent(10)
    // 1. 获取节点信息
    const fetchNodeInfo = async () => {
      try {
        const _nodeInfo = await fetch(NODES_REQUEST_URL).then((res) => res.json())
        setPercent(20)
        dispatch(setNodeInfo(_nodeInfo))
        return _nodeInfo
      } catch (error) {
        console.error('获取节点信息失败:', error)
      }
    }

    // 2. 资源预加载
    const loadResources = async (_nodeInfo: IEarthConfigInfo) => {
      try {
        if (_nodeInfo.index_page.pre_download_res) {
          // 收集所有类型的资源：视频、音频、图片等
          const allResources = _.flattenDeep((_nodeInfo.node_groups || [])
            .map((item) => item.nodes.map((node) => node.res))).filter((i) => i)

          // 如果有音频资源，也添加到预加载列表中
          const audioResources: string[] = []
          if (_nodeInfo.index_page?.audio_url) {
            audioResources.push(_nodeInfo.index_page.audio_url)
          }

          // 合并所有资源
          const resources: any[] = [...allResources, ...audioResources]

          const onProgress = (_percent: number) => {
            updateProgress('resources', _percent)
          }

          // 使用通用资源预加载器
          await resourcePreloader(resources, { onProgress })
        } else {
          // 如果不需要预加载资源，直接标记完成
          updateProgress('resources', 1)
        }
      } catch (error) {
        console.error('资源加载失败:', error)
        updateProgress('resources', 1) // 即使失败也标记完成
      }
    }

    // 2. SDK初始化
    const initSDK = async () => {
      try {
        const userAgent = navigator.userAgent.toLowerCase()
        const isAndroid = /android/.test(userAgent)
        const appKey = isAndroid ? 'gamified_android' : 'gamified_web'
        await DataTracker.getInstance().init({
          // 环境配置
          env: 'test', // test, preproduction, product
          // 根据平台设置不同的appKey

          // 采集SDK配置
          sensors: {
            serverUrl: 'https://data-collect-service-pro.sdp.101.com/v1/data_collect',
            appKey, // 需要申请获得
            bridge: true, // 是否开启app代理上报
            sessionTimeout: 60 * 60 * 1000, // 会话超时时间
          },

          // 初始化完成回调
          callback: ({ sensors }) => {
            console.log('埋点SDK初始化完成', sensors)
          },
          // 必传参数
          qt: false,
        })

        updateProgress('sdk') // SDK初始化完成
      } catch (error) {
        console.error('SDK初始化失败:', error)
        updateProgress('sdk') // 即使失败也标记完成
      }
    }

    fetchNodeInfo().then((_nodeInfo) => {
      // 并行执行资源加载和SDK初始化
      loadResources(_nodeInfo)
      initSDK()
    })
  }, []) // 只在组件挂载时执行一次

  return (
    <div className={cn('start-home', { guided: guided.home })}>
      {/* 欢迎音频
      介绍音频 */}
      <Progress
        percent={percent}
        className={cn('progress')}
      />
      <img src={startButton} onClick={onStart} className={cn('start-button', { visible: percent === 100 })} />
      <audio src={nodeInfo?.index_page?.audio_url} autoPlay style={{ width: 0, height: 0 }} />
    </div>
  )
}

export default Home
