.setting {
  position: fixed;
  left: 1.25rem;
  top: 1.125rem;
  z-index: 3;
  &.guided {
    z-index: 999;
    .exit-btn-wrapper {
      opacity: 0.5;
    }
    .setting-btn-wrapper {
      z-index: 999;
    }
  }
  .exit-btn-wrapper,
  .setting-btn-wrapper {
    position: relative;
    display: inline-block;
    cursor: pointer;
    width: 2.1875rem;
    height: 2.1875rem;
    transition: transform 0.1s ease;
    user-select: none;
  }

  .setting-btn-wrapper {
    margin-left: 1.25rem;
  }

  .exit-btn-bg,
  .setting-btn-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 2.1875rem;
    height: 2.1875rem;
    pointer-events: none;
    transition: opacity 0.1s ease;
  }

  .exit-btn-icon,
  .setting-btn-icon {
    position: absolute;
    top: 0.4375rem;
    left: 0.475rem;
    width: 1.25rem;
    height: 1.25rem;
    pointer-events: none;
    transition: opacity 0.1s ease;
  }

  .exit-btn-icon,
  .setting-btn-icon {
    z-index: 2;
  }

  .exit-btn-bg,
  .setting-btn-bg {
    z-index: 1;
  }

  // 鼠标按下效果，兼容移动和PC
  .exit-btn-wrapper:active .exit-btn-bg {
    background: url("../../../../asset/images/header_left/general_icon_bg_pre.png") no-repeat center;
    background-size: 2.1875rem 2.1875rem;
    opacity: 1;
  }

  .setting-btn-wrapper:active .setting-btn-bg {
    background: url("../../../../asset/images/header_left/general_icon_bg_pre.png") no-repeat center;
    background-size: 2.1875rem 2.1875rem;
    opacity: 1;
  }

  // 设置按钮开启状态按下效果
  .setting-btn-wrapper.active:active .setting-btn-bg {
    background: url("../../../../asset/images/header_left/general_icon_bg_act_pre.png") no-repeat
      center;
    background-size: 2.1875rem 2.1875rem;
  }
}

.setting-popover {
  font-size: 1rem;
  :global {
    .fish-radio-wrapper {
      font-size: 1rem;
      line-height: 1.3;
    }
    .fish-radio-input {
      width: 1rem;
      height: 1rem;
    }

    .fish-radio {
      font-size: 1rem;
      width: 1rem;
      height: 1rem;
      top: -0.1rem;
    }

    .fish-popover-arrow {
      display: none;
    }

    .fish-popover-inner {
      background: transparent;
      box-shadow: none;
    }
    .fish-popover-inner-content {
      padding: 0rem;
    }
    .fish-radio-inner {
      width: 1rem;
      height: 1rem;
      border-color: #6170e7;
      transition: unset;
    }
    .fish-radio-inner::after {
      background-color: #6170e7;
      width: 0.5rem;
      height: 0.5rem;
      top: 0.1875rem;
      left: 0.1875rem;
      border-radius: 0.5rem;
      transition: unset;
    }
    .fish-radio-wrapper-checked {
      &.label {
        color: #6170e7;
      }
    }
    .fish-radio-checked::after {
      border: 1px solid #6170e7;
      animation: unset;
    }
  }
  .setting-content {
    width: 18.125rem;
    height: 7.5rem;
    background: url("../../../../asset/images/header_left/pop_bg.png") no-repeat center center/
      contain;
    background-size: 100% 100%;
    padding: 1.25rem 0.9375rem;
    margin-left: 0.9375rem;
    &.aiMode {
      height: 23.125rem;
      background: url("../../../../asset/images/pop_bg.png") no-repeat center center/ contain;
    }
    .title,
    .label {
      color: #323232;
      font-family: "Alibaba PuHuiTi";
      font-style: normal;
      font-weight: 700;
      &.active {
        span {
          color: #6170e7;
        }
      }
    }
    .title {
      margin-bottom: 1.5rem;
    }
    .sub_label {
      color: rgba(0, 0, 0, 0.3);
      font-family: "Alibaba PuHuiTi";
      font-style: normal;
      font-weight: 500;
    }
    .achievement-switch {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .title {
        margin-bottom: 0rem;
      }
      .achievement-switch-toggle {
        margin-right: 1.25rem;
      }
    }
  }
}
