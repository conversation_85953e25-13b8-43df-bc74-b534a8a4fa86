import React, { useEffect, useImperativeHandle, forwardRef, useState } from 'react'
import classNames from 'classnames/bind'
import { useAppDispatch, useAppSelector } from '@/store'
import _ from 'lodash'
import EventBus from '@/component/EventBus'
import { Events } from '@/component/EventBus/constant'
import tipSetting from '@/asset/images/guide/guide_setting.png'
import tipTitle from '@/asset/images/guide/guide_title.png'
import tipBook from '@/asset/images/guide/guide_book.png'
import dialogueBg from '@/asset/images/guide/dialogue_text.png'
import AudioManager from '@/util/AudioManager'
import guideMusic from '@/asset/musics/home/<USER>'
import guide2Music from '@/asset/musics/home/<USER>'
import styles from './index.module.scss'
import { updateData } from '../../earthSlice'

const cn = classNames.bind(styles)

export interface GuideRef {
  onDone: () => void
}

const Guide = forwardRef<GuideRef>((props, ref) => {
  const { data } = useAppSelector((state) => state.earth)
  const dispatch = useAppDispatch()
  const [currentIndex, setCurrentIndex] = useState(0)

  const { guided } = data

  const onDone = () => {
    if (currentIndex === 0) {
      setCurrentIndex(1)
      AudioManager.getInstance('sfx').stop()
      AudioManager.getInstance('sfx').play(guide2Music)
      return
    }
    const newGuided = _.cloneDeep(guided)
    dispatch(updateData({
      guided: {
        ...newGuided,
        home: true
      }
    }))
    EventBus.emit(Events.GuideHome)
    AudioManager.getInstance('sfx').stop()
  }

  useEffect(() => {
    AudioManager.getInstance('sfx').play(guideMusic)
    return () => {
      AudioManager.getInstance('sfx').stop()
    }
  }, [])

  useImperativeHandle(ref, () => ({
    onDone
  }), [guided, dispatch])

  useEffect(() => {
    const handleClick = () => {
      onDone()
      document.removeEventListener('click', handleClick)
    }
    document.addEventListener('click', handleClick)
    return () => {
      document.removeEventListener('click', handleClick)
    }
  }, [onDone])

  const renderIndex = () => {
    if (currentIndex === 0) {
      return (
        <div className={cn('dialogue-content')}>
          完成
          <span>地球内部结构</span>
          探究，需要闯五关： 挖线索提
          <span>问题</span>
          →升级问题为
          <span>假设</span>
          →边
          <span>实验</span>
          边校准→定
          <span>结论</span>
          做讲解→
          <span>反思</span>
          和讨论。
        </div>
      )
    } else {
      return (
        <div className={cn('dialogue-content')}>
          每一关都是向地球内部结构的真相更进一步！准备好了吗？
        </div>
      )
    }
  }

  return (
    <div className={cn('guide')}>
      <img src={tipSetting} className={cn('guide_setting')} />
      <img src={tipTitle} className={cn('guide_title')} />
      <img src={tipBook} className={cn('guide_book')} />
      <img src={tipBook} className={cn('guide_book')} />
      <div className={cn('dialogue')}>
        <img src={dialogueBg} />
        {renderIndex()}
      </div>
    </div>
  )
})

export default Guide
