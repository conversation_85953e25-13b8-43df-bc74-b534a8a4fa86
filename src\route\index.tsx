// 页面组件
import { asyncLoader } from '@/util/loader'
import { Navigate, type RouteObject } from 'react-router-dom'
import React from 'react'
import wordRoute from './earth'

// 路由引入

const routerConfig: RouteObject[] = [
  wordRoute,
  {
    path: '/',
    index: true,
    element: <Navigate to="earth" />
  },
  { // 兜底
    path: '*',
    element: asyncLoader(() => import('../component/status/404'))
  }
]

export default routerConfig
