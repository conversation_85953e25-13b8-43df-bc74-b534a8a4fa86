/**
 * 注意，此文件被内部 node 代码使用，请勿使用 import/export 语法
 */
const { wdsProxySetting } = require('@gem-mine/request').default
/**
 * 请求配置，格式为：
 * {
 *   [请求域集合的名称]: {
 *     [环境1]: {
 *       url: 请求地址,
 *       prefix: 请求前缀
 *     },
 *     [环境2]: {
 *       url: 请求地址,
 *       prefix: 请求前缀
 *     }
 *   },
 *   [另一个请求域集合的名称]: {}
 * }
 */
const config = {
  aic: {
    defaults: {
      prefix: 'v1.0',
      url: 'https://aic-service.debug.ndaeweb.com'
    },
    test: {
      prefix: 'v1.0',
      url: 'https://aic-service.debug.ndaeweb.com'
    },
    preproduction: {
      prefix: 'v1.0',
      url: 'https://aic-service.beta.101.com'
    },
    product: {
      prefix: 'v1.0',
      url: 'https://aic-service.sdp.101.com'
    }
  },
  ocr: {
    defaults: {
      prefix: 'v1.0',
      url: 'https://interaction-server.sdp.101.com'
    },
    test: {
      prefix: 'v1.0',
      url: 'https://interaction-server.sdp.101.com'
    },
    preproduction: {
      prefix: 'v1.0',
      url: 'https://interaction-server.sdp.101.com'
    },
    product: {
      prefix: 'v1.0',
      url: 'https://interaction-server.sdp.101.com'
    }
  },
}

exports.config = config
exports.proxyConfig = wdsProxySetting(config)
