/**
 * 数据埋点 SDK 单例类
 * 基于 @x-edu/tracker 的封装
 */
import { XTracker } from '@x-edu/tracker'

// 定义全局环境变量类型
declare global {
  interface Window {
    __global_env?: string
  }
}

// 定义初始化配置接口
interface TrackerInitConfig {
  /** 环境配置 */
  env?: string
  /** 采集SDK初始化配置参数 */
  sensors: {
    /** 采集数据接收地址 */
    serverUrl: string
    /** 应用的AppKey */
    appKey: string
    /** 是否开启 app 代理上报 */
    bridge?: boolean
    /** 会话超时时间，单位为毫秒 */
    sessionTimeout?: number
  }
  /** 完成 SDK 加载后的回调函数 */
  callback?: (params: { sensors: any }) => void
  /** 必传参数 */
  qt?: any
}

// 定义点击事件参数接口
interface TrackEventParams {
  /** 事件编码 */
  eventName: string
  /** 事件参数 */
  params?: {
    /** 事件类型 */
    event_type?: string
    /** 其他业务参数 */
    [key: string]: any
  }
}

// 定义PV事件参数接口
interface PVEventParams {
  /** 事件编码 */
  eventName: string
  /** 事件参数 */
  params?: {
    [key: string]: any
  }
}

/**
 * 数据埋点单例类
 */
class DataTracker {
  private static instance: DataTracker

  private XTracker: any = null

  private isInitialized: boolean = false

  private isInitializing: boolean = false

  private initPromise: Promise<void> | null = null

  // eslint-disable-next-line @typescript-eslint/no-empty-function
  private constructor() { }

  /**
   * 获取单例实例
   */
  public static getInstance(): DataTracker {
    if (!DataTracker.instance) {
      DataTracker.instance = new DataTracker()
    }
    return DataTracker.instance
  }

  /**
   * 初始化埋点SDK
   * @param config 初始化配置
   */
  public init(config: TrackerInitConfig): Promise<void> {
    // 如果已经初始化完成，直接返回
    if (this.isInitialized) {
      console.warn('DataTracker has already been initialized')
      return Promise.resolve()
    }

    // 如果正在初始化中，返回已存在的Promise
    if (this.isInitializing && this.initPromise) {
      return this.initPromise
    }

    this.isInitializing = true

    this.initPromise = new Promise<void>((resolve, reject) => {
      try {
        this.XTracker = XTracker

        // 设置默认配置，确保env有默认值
        const envValue = config.env || window.__global_env || 'ncet-xedu'

        const initConfig = {
          env: envValue,
          sensors: {
            serverUrl: config.sensors.serverUrl,
            appKey: config.sensors.appKey,
            bridge: config.sensors.bridge !== undefined ? config.sensors.bridge : true,
            sessionTimeout: config.sensors.sessionTimeout || 60 * 60 * 1000,
          },
          callback: (params: any) => {
            this.isInitialized = true
            this.isInitializing = false
            console.log('DataTracker initialized successfully')

            // 调用用户自定义回调
            if (config.callback) {
              config.callback(params)
            }

            resolve()
          },
          qt: config.qt || false,
        };
        (XTracker as any).init(initConfig)
      } catch (error) {
        this.isInitializing = false
        console.error('DataTracker initialization failed:', error)
        reject(error)
      }
    })

    return this.initPromise
  }

  /**
   * 上报点击行为事件
   * @param params 事件参数
   */
  public track(params: TrackEventParams): void {
    if (!this.isInitialized || !this.XTracker) {
      console.warn('DataTracker is not initialized. Please call init() first.')
      return
    }

    try {
      const trackParams = {
        eventName: params.eventName,
        params: {
          event_type: 'clickEvent',
          ...params.params,
        },
      }

      this.XTracker.track(trackParams)
      console.log('Track event sent:', trackParams)
    } catch (error) {
      console.error('Failed to track event:', error)
    }
  }

  /**
   * 上报页面访问事件
   * @param params 事件参数
   */
  public pv(params: PVEventParams): void {
    if (!this.isInitialized || !this.XTracker) {
      console.warn('DataTracker is not initialized. Please call init() first.')
      return
    }

    try {
      const pvParams = {
        eventName: params.eventName,
        params: params.params || {},
      }

      this.XTracker.pv(pvParams)
      console.log('PV event sent:', pvParams)
    } catch (error) {
      console.error('Failed to send PV event:', error)
    }
  }

  /**
   * 检查是否已初始化
   */
  public isReady(): boolean {
    return this.isInitialized
  }

  /**
   * 获取原始XTracker实例（供高级用法使用）
   */
  public getXTracker(): any {
    if (!this.isInitialized || !this.XTracker) {
      console.warn('DataTracker is not initialized. Please call init() first.')
      return null
    }
    return this.XTracker
  }
}

// 同时导出类和接口供外部使用
export {
  DataTracker,
  TrackerInitConfig,
  TrackEventParams,
  PVEventParams,
}
