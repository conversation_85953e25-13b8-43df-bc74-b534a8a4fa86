import React, {
  FC, useEffect, useRef, useState
} from 'react'
import classNames from 'classnames/bind'
import { Input } from 'fish'
import { useLatest } from 'ahooks'
import { random } from 'lodash'
import wheelTopPng from '@/asset/images/books/hypothesis/wheel/wheel_top.png'
import wheelTurnPng from '@/asset/images/books/hypothesis/wheel/wheel_self.png'
import pointerOffPng from '@/asset/images/books/hypothesis/wheel/wheel_off.png'
import pointerOnPng from '@/asset/images/books/hypothesis/wheel/wheel_on.png'
import closePng from '@/asset/images/books/hypothesis/wheel/divergence_off.png'
import EventBus from '@/component/EventBus'
import { Events } from '@/component/EventBus/constant'
import { getElementPosition } from '@/util/box'
import styles from './index.module.scss'
import { TargetBox } from './DndComponent'
import { WHEEL_TABS } from '../../constant'

const cn = classNames.bind(styles)

interface IWheelProps {
  onChange: (data) => void
  onClose: () => void
  getRelationImg: (rc) => string | undefined
}
const Wheel: FC<IWheelProps> = ({
  onChange,
  onClose,
  getRelationImg
}) => {
  const [tempRc, setTempRc] = useState('')
  const [tempQuestion, setTempQuestion] = useState('')
  const [angle, setAngle] = useState(36)
  const [index, setIndex] = useState(-1)
  const [isRunning, setIsRunning] = useState(false)
  const lastAngle = useLatest(angle)
  const animationRef = useRef({
    startTime: 0,
    currentVelocity: 0,
    rafId: 0
  })
  const clueRef = useRef<HTMLDivElement>(null)

  const onStart = () => {
    setIsRunning(true)
    setAngle(36) // 重置角度，加36度
    setIndex(-1) // 重置索引
    // 初始化动画参数
    animationRef.current = {
      startTime: performance.now(),
      currentVelocity: 720, // 初始角速度720度/秒
      rafId: 0
    }

    const animate = (timestamp) => {
      const { startTime, currentVelocity } = animationRef.current
      const elapsed = (timestamp - startTime) / 1000 // 转换为秒
      if (elapsed < 3) {
        const decayedVelocity = currentVelocity * random(0.9, 1.1) // 模拟阻尼效果
        const deltaAngle = decayedVelocity * (1 / 36) // 假设60fps

        setAngle((prev) => {
          const newAngle = prev + deltaAngle
          animationRef.current.currentVelocity = decayedVelocity
          animationRef.current.rafId = requestAnimationFrame(animate)
          return newAngle
        })
      } else {
        // 动画结束计算最终位置
        const finalAngle = lastAngle.current % 360
        let _index = Math.floor(finalAngle / (360 / WHEEL_TABS.length))// 每个区块72度
        const offset = finalAngle % (360 / WHEEL_TABS.length)
        if (offset > 36) {
          // 如果偏移超过36度，则向下取整
          _index = (_index + 1) % WHEEL_TABS.length
        }
        console.log(` 最终角度：${finalAngle.toFixed(2)}°，offset:${offset} ，对应区块：${_index}`)
        setAngle(_index * 72)
        setIndex(_index)
        cancelAnimationFrame(animationRef.current.rafId)
        setIsRunning(false)
      }
    }

    animationRef.current.rafId = requestAnimationFrame(animate)
  }

  // 组件卸载时清除动画
  useEffect(() => () => {
    if (animationRef.current.rafId) {
      cancelAnimationFrame(animationRef.current.rafId)
    }
  }, [])

  const onDrop = (rc_id) => {
    setTempRc(rc_id)
  }
  const onChangeQuestion = (e) => {
    if (!tempRc) {
      const position = getElementPosition(clueRef.current!)

      EventBus.emit(Events.fingerTip, {
        text: '请先关联线索',
        direction: 'left',
        position: { x: position.x + 60, y: position.y + 20 },
      })
      return
    }
    setTempQuestion(e.target.value)
  }
  return (
    <div className={cn('wheel')}>
      <img src={closePng} onClick={onClose} className={cn('close')} />
      <div className={cn('header')}>
        问题发散器
      </div>
      <div className={cn('relation')}>
        <div className={cn('clue-box')}>
          <div className={cn('relation-clue')} ref={clueRef}>
            <TargetBox can onDrop={({ id: rc_id }) => { onDrop(rc_id) }} wheel hasRc={!!tempRc}>
              {tempRc && <img className={cn('relation-clue-img')} src={getRelationImg(tempRc)} />}
            </TargetBox>
          </div>
          <div className={cn('relation-clue-text')}>
            将左侧线索拖入此框
          </div>
        </div>
        <div className={cn('question')}>
          <Input value={tempQuestion} onChange={onChangeQuestion} />
          <div className={cn('question-submit')} onClick={() => { onChange(tempQuestion) }} />
        </div>
      </div>

      <div className={cn('luck')}>
        {
          index >= 0 && (
            <div className={cn('luck-index')}>
              {
                WHEEL_TABS[index].labels.map((label) => (
                  <div key={label} className={cn('luck-label')}>
                    {label}
                  </div>
                ))
              }
            </div>
          )
        }
        <img
          style={{ transform: `rotate(${angle}deg)` }}
          src={wheelTurnPng}
          className={cn('turn')}
        />
        <img src={wheelTopPng} className={cn('default')} />

        <img
          src={isRunning ? pointerOnPng : pointerOffPng}
          className={cn('pointer', { on: isRunning })}
          onClick={isRunning ? undefined : onStart}
        />
      </div>
    </div>
  )
}

export default Wheel
