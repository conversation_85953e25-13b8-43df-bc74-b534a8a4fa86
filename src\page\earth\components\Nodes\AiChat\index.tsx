/* eslint-disable @typescript-eslint/no-use-before-define */
import React, { useEffect, useRef, useState } from 'react'
import classNames from 'classnames/bind'
import Chat, { IChatRef } from '@/component/chat'
import { IMessage, ITtsConfig } from '@/component/chat/interface'
import ScreenshotCard from '@/page/earth/components/Nodes/Animation/Screenshot/ScreenshotCard'
import EventBus from '@/component/EventBus'
import { Events } from '@/component/EventBus/constant'
import { useAppDispatch, useAppSelector } from '@/store'
import { updateData } from '@/page/earth/earthSlice'
import { getTtsToken } from '@/page/earth/network'
import AudioManager from '@/util/AudioManager'
import styles from './index.module.scss'

const cn = classNames.bind(styles)

const AiChat = () => {
  const { data } = useAppSelector((state) => state.earth)
  const { ai, config } = data
  const chatRef = useRef<IChatRef>(null)
  // 临时存 不全局入库
  const [messages, setMessages] = useState<IMessage[]>([])
  const [ttsConfig, setTtsConfig] = useState<ITtsConfig>({
    enabled: true,
    appid: '',
    stsToken: '',
    speaker: '',
    resourceId: '',
  })
  const [hide, setHide] = useState(false)
  const dispatch = useAppDispatch()
  useEffect(() => {
    console.log('消息变化:', messages)
  }, [messages])

  const screenshotCardRef = useRef<any>(null)

  // 处理数据变化
  const onMessagesChange = (newMessages: IMessage[]) => {
    setMessages(newMessages)
  }

  const onLike = (messageId: string) => {
    setMessages((prevMessages) => prevMessages.map((msg) => (msg.id === messageId ? {
      ...msg,
      meta: {
        ...msg.meta || {},
        liked: !msg.meta?.liked
      }
    } : msg)))
  }

  const onScreenshot = (messageId: string) => {
    // 找到对应的消息
    const message = messages.find((m) => m.id === messageId)
    if (!message) {
      console.error('Message not found:', messageId)
      return
    }

    console.log('准备截图，消息内容:', message.content)

    // 隐藏聊天窗口
    chatRef.current?.closeChat()

    // 开始截图
    screenshotCardRef?.current.capture({
      title: '与馆长对话',
      content: message.content,
    }, () => {
      chatRef.current?.openChat()
    })
  }

  const getTokenInfo = () => {
    getTtsToken({
      license_key: 'EkQHCrPMuFp2znxMtb5A' // 固定值
    }).then((res) => {
      setTtsConfig({
        ...res.tts,
        enabled: true,
        appid: res.tts.appid,
        stsToken: res.tts.sts_token,
        speaker: res.tts.voice_type,
        resourceId: 'volc.service_type.10029',
      })
      setTimeout(() => {
        getTokenInfo()
      }, 86400000) // 每天更新一次
    })
  }

  const onOpen = (_data) => {
    if (_data.name === 'chat') {
      chatRef.current?.openChat()
      onSendPrompt(_data.prompt)
    }
  }

  const showNotification = ({ content, duration = 2000, music }) => {
    if (config.aiMode) {
      chatRef.current?.showNotification(content, duration)
      if (music) {
        AudioManager.getInstance('sfx').play(music)
      }
    }
  }

  const onAiGuide = (_data) => {
    setHide(!!_data)
  }

  useEffect(() => {
    getTokenInfo()
    EventBus.on(Events.OpenComponent, onOpen)
    EventBus.on(Events.ShowNotification, showNotification)
    EventBus.on(Events.aiGuide, onAiGuide)

    return () => {
      EventBus.off(Events.OpenComponent, onOpen)
      EventBus.off(Events.ShowNotification, showNotification)
      EventBus.off(Events.aiGuide, onAiGuide)
      AudioManager.getInstance('sfx').stop()
    }
  }, [])

  const onClickOpen = () => {
    // Ai主动点击次数加1
    dispatch(updateData({ ai: { activeCount: ai.activeCount + 1 } }))
    onSendPrompt()
  }
  const onSendPrompt = (prompt = '') => {
    // todo提示词，还有实验失败的
    let promptContent = prompt
    if (!promptContent) {
      promptContent = `
        ##角色
你是一位地质博物馆的老馆长，你正在负责一个小学生地质科普的项目。

##技能
1. 你非常了解地球内部结构，及其与火山喷发、地壳运动、地震等自然现象的联系
2. 你特别擅长以深入浅出的方式，为非专业人士、儿童等群体解释专业知识
3. 你采取启发式教学，通过提供必要的信息，引导学生自己思考、推导出结论，而不是直接灌输结论给学生

##前置信息
-学习目标 
地球内部结构包括地壳、地幔和地核。地壳的岩石包括沉积岩、变质岩和岩浆岩，且地壳是在不断运动着的。
-目标用户 
小学五年级学生
-学习环节描述 
学生将进入一个虚拟3D的火山喷发场景进行观察，他们将收集到如下线索：
1. 火山岩浆，高温熔融的岩石物质
2. 地表裂缝，岩浆上涌产生巨大推力，让岩层断裂

##学生操作数据
-学生已收集线索： 
--传入用户已收集到的线索数据，如线索1：XXXX 
-学生修订的线索信息 
--线索1：XXXX 
--线索2：XXXX 

##任务
首先对学生的提问表示出赞赏，然后以通俗易懂的、简洁的语言回答学生的问题。

##输出的约束条件
1. 输出内容的难度需要符合五年级小学生的认知水平
2. 不能直接输出学习目标的内容，如学生询问，如：“地球内部结构是什么？”，请基于学习环节中出现的线索给予学生引导
3. 不要出现“地壳、地幔、地核、沉积岩、变质岩、岩浆岩”等学习目标中的术语
4. 每次输出内容在100字以内，语言简洁、通俗易懂
5. 如果学生询问与学习目标无关的话题，请引导学生到学习目标的思考和探究上

##样例
-学生问题 
为什么地下会冒出来岩浆？
-回复 
这是个很棒的问题！地球内部可不只是我们肉眼可见的这些岩石，它的内部非常热，还有非常大的压力。由于地球内部极高的温度和压力，部分岩石会发生熔融，形成岩浆。当岩浆通过地表的薄弱带上升并喷出地表时，便形成火山喷发，出现了你看到的岩浆。

##初始化
对用户说欢迎语，类似“让我看看这个聪明的小脑袋里冒出来什么问题了？”，然后待用户提问后，回答用户问题。不要输出欢迎语之外的附加内容。
        `
    }
    // 处理提示词
    chatRef.current?.sendMessage(promptContent, 'system')
  }

  const onCloseWindow = () => {
    setMessages([])
  }

  return (
    <>
      <Chat
        ref={chatRef}
        title="与馆长对话"
        width="33.4375rem"
        height="40.3125rem"
        defaultPosition={{ x: 0, y: 0 }}
        messages={messages}
        onMessagesChange={onMessagesChange}
        onTtsConfigChange={setTtsConfig}
        ttsConfig={ttsConfig}
        enableScreenshot
        enableLike
        enableVoice
        enableRegenerate
        className={cn('ai-chat-widget', { hide })}
        onScreenshot={onScreenshot}
        onLike={onLike}
        onVoice={(messageId) => {
          console.log('语音消息ID:', messageId)
        }}
        onClick={onClickOpen}
        onClose={onCloseWindow}
      />

      <ScreenshotCard
        ref={screenshotCardRef}
      />
    </>
  )
}

export default AiChat
