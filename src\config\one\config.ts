import Constant from '../constant'

export default {
  env: Constant.ONE_ENV,
  // name: 'one',       // 类型：string, 默认 'one'
  sdpAppId: Constant.SDP_APP_ID, // 类型：string, 默认 从url获取
  // headers: {},       // 类型：object, 默认 headersPlugin()
  // request: {},       // 类型：object, 默认 requestPlugin()
  // uc: {},            // 类型：object, 可以传入自定义的uc sdk初始化参数，默认{}
  // oms: true,         // 类型：boolean | object, 可以传入自定义的oms sdk初始化参数，默认为true（开启oms）
  // rbac: false,       // 类型：boolean | object, 可以传入自定义的rbac sdk初始化参数，默认为false（不开启rbac）
  uc: {
    autoRefresh: true,
    storageExpire: 60 * 24 * 60 * 60,
    noEnvUseProduct: true
  }
}
