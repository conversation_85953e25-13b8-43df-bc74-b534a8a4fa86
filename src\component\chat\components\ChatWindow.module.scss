.chatWindow {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  z-index: 10000;
  .chatMessages {
    height: 100%;
    display: flex;
    flex-direction: column;
    border-radius: 0.75rem;
    overflow: hidden;
    padding: 0.5rem 0.625rem;
    background: url("../images/widow_bg.png") no-repeat center center;
    background-size: 100% 100%;
  }
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: white;

  .title {
    font-size: 1rem;
    font-weight: 600;
    img{
      width: 1.25rem;
      margin-right: 1rem;
    }
  }

  .actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;

    .speechSwitch {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      font-size: 0.75rem;
      color: rgba(255, 255, 255, 0.9);
    }

    .interruptBtn,
    .closeBtn {
      background: none;
      border: none;
      color: white;
      font-size: 1rem;
      cursor: pointer;
      padding: 0.25rem 0.5rem;
      border-radius: 0.25rem;
      transition: background-color 0.2s ease;

      &:hover {
        background: rgba(255, 255, 255, 0.2);
      }
    }
  }
}

.messageList {
  margin-top: 0.625rem;
  border-radius: 0.75rem;
  flex: 1;
  overflow: auto;
  padding: 0.625rem;
  background: url("../images/message_bg.png") no-repeat center center/cover;
  position: relative;
  min-height: 12.5rem; // 确保有最小高度

  // 为react-virtualized容器添加样式
  .ReactVirtualized__Grid {
    outline: none !important;
    padding: 1rem 0 1rem 0 !important; // 上下增加padding确保内容不被截断

    // 滚动条样式
    &::-webkit-scrollbar {
      width: 0.375rem;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(0, 0, 0, 0.2);
      border-radius: 0.1875rem;

      &:hover {
        background: rgba(0, 0, 0, 0.3);
      }
    }
  }

  .emptyState {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: #999;
    padding: 2.5rem 1.25rem;

    .emptyIcon {
      font-size: 3rem;
      margin-bottom: 1rem;
      opacity: 0.6;
    }

    .emptyText {
      font-size: 1rem;
      text-align: center;
      line-height: 1.5;
    }
  }
}

.inputContainer {
  margin-top: 1.25rem;

  .textInput::placeholder {
    font-size: 1.125rem;
    text-align: center;
  }
  .textInput {
    width: 100%;
    border: 0.0625rem solid #d9d9d9;
    border-radius: 0.5rem;
    padding: 0.75rem;
    font-size: 0.875rem;
    line-height: 1.5;
    resize: none;
    min-height: 2.5rem;
    max-height: 7.5rem;
    transition: border-color 0.3s ease;

    &:focus {
      outline: none;
      border-color: #1890ff;
      box-shadow: 0 0 0 0.125rem rgba(24, 144, 255, 0.2);
    }

    &:disabled {
      background: #f5f5f5;
      cursor: not-allowed;
    }

    &::placeholder {
      color: #bfbfbf;
    }
  }

  .sendBtn {
    width: 2.5rem;
    height: 2.5rem;
    border: none;
    background: #1890ff;
    color: white;
    border-radius: 0.5rem;
    font-size: 1rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    flex-shrink: 0;

    &:hover:not(:disabled) {
      background: #40a9ff;
      transform: translateY(-0.0625rem);
    }

    &:active:not(:disabled) {
      transform: translateY(0);
    }

    &:disabled {
      background: #d9d9d9;
      cursor: not-allowed;
      transform: none;
    }

    &.loading {
      animation: pulse 1.5s ease-in-out infinite;
    }
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
