.customSwitch {
  position: relative;
  display: inline-block;
  box-sizing: border-box;
  min-width: 2.75rem;
  height: 1.375rem;
  line-height: 1.375rem;
  vertical-align: middle;
  border: 0;
  border-radius: 6.25rem;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  user-select: none;
  background: rgba(0, 0, 0, 0.25);
  outline: none;

  &:focus-visible {
    box-shadow: 0 0 0 0.125rem rgba(24, 144, 255, 0.2);
  }

  .handle {
    position: absolute;
    top: 0.125rem;
    left: 0.125rem;
    width: 1.125rem;
    height: 1.125rem;
    border-radius: 50%;
    background: #fff;
    box-shadow: 0 0.125rem 0.25rem 0 rgba(0, 35, 11, 0.2);
    transition: all 0.2s ease-in-out;
    cursor: pointer;
  }

  .inner {
    display: block;
    margin-right: 0.125rem;
    color: #fff;
    font-size: 0.75rem;
    transition: all 0.2s ease-in-out;
    text-align: right;
    pointer-events: none;
    line-height: 1.275rem;
  }

  // 选中状态
  &.checked {
    background: #1890ff;

    .handle {
      left: calc(100% - 1.25rem);
    }

    .inner {
      text-align: left;
    }
  }

  // 禁用状态
  &.disabled {
    opacity: 0.4;
    cursor: not-allowed;

    .handle {
      cursor: not-allowed;
    }
  }

  // 悬停状态
  &:not(.disabled):hover {
    &.checked {
      background: #40a9ff;
    }

    &:not(.checked) {
      background: rgba(0, 0, 0, 0.35);
    }
  }
}

// 加载状态动画
@keyframes switch-loading {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// 可选：为开关添加加载状态
.customSwitch.loading {
  .handle::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0.75rem;
    height: 0.75rem;
    margin: -0.375rem 0 0 -0.375rem;
    border: 0.125rem solid transparent;
    border-top-color: #1890ff;
    border-radius: 50%;
    animation: switch-loading 0.8s linear infinite;
  }
}
