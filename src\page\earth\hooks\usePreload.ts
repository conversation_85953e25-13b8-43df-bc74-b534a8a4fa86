const usePreloader = () => {
  // 检测是否为移动端
  const isMobile = () => /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)

  // 检测是否为低功耗模式或数据节省模式
  const isLowPowerMode = () => {
    // 检测连接类型
    const connection = (navigator as any).connection || (navigator as any).mozConnection || (navigator as any).webkitConnection
    if (connection) {
      // 如果是慢速连接，不进行预加载
      return connection.effectiveType === 'slow-2g' || connection.effectiveType === '2g' || connection.saveData
    }
    return false
  }

  // 根据URL扩展名判断资源类型
  const getResourceType = (url: string): 'video' | 'audio' | 'image' | '3d' | 'unknown' => {
    const ext = url.split('.').pop()?.toLowerCase() || ''

    // 视频格式
    if (['mp4', 'webm', 'ogg', 'avi', 'mov', 'wmv', 'flv', 'm4v'].includes(ext)) {
      return 'video'
    }

    // 音频格式
    if (['mp3', 'wav', 'ogg', 'aac', 'm4a', 'flac', 'wma'].includes(ext)) {
      return 'audio'
    }

    // 图片格式
    if (['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg', 'bmp', 'ico'].includes(ext)) {
      return 'image'
    }

    // 3D相关格式
    if (['gltf', 'glb', 'obj', 'fbx', 'dae', 'hdr', 'ktx2', 'ply', 'stl', '3ds', 'blend'].includes(ext)) {
      return '3d'
    }

    return 'unknown'
  }

  // 通用资源预加载器
  const resourcePreloader = (urls: string[], { onProgress }: { onProgress?: (progress: number) => void } = {}) => {
    const resources: Record<string, any> = {}
    let loadedCount = 0
    const total = urls.length
    const mobile = isMobile()
    const lowPower = isLowPowerMode()

    const handleProgress = () => {
      loadedCount += 1
      onProgress?.(loadedCount / total) // 返回 0~1 的进度值
    }

    // 如果没有资源需要加载，直接完成
    if (total === 0) {
      onProgress?.(1)
      return resources
    }

    // 如果是移动端且处于低功耗模式，直接返回空的resources对象
    if (mobile && lowPower) {
      // 模拟加载完成
      setTimeout(() => {
        for (let i = 0; i < total; i++) {
          handleProgress()
        }
      }, 100)
      return resources
    }

    urls.forEach((url) => {
      const resourceType = getResourceType(url)
      let element: HTMLVideoElement | HTMLAudioElement | HTMLImageElement | null = null
      let isHandled = false // 防止重复处理

      // 根据资源类型创建对应的元素
      switch (resourceType) {
        case 'video':
          element = document.createElement('video')
          // 移动端优化设置
          if (mobile) {
            const videoElement = element as HTMLVideoElement
            videoElement.preload = 'metadata'
            videoElement.muted = true
            videoElement.playsInline = true
            element.setAttribute('webkit-playsinline', 'true')
            element.setAttribute('x5-playsinline', 'true')
            element.setAttribute('x5-video-player-type', 'h5-page')
          } else {
            const videoElement = element as HTMLVideoElement
            videoElement.preload = 'auto'
          }
          break

        case 'audio':
          element = document.createElement('audio')
          if (mobile) {
            const audioElement = element as HTMLAudioElement
            audioElement.preload = 'metadata'
          } else {
            const audioElement = element as HTMLAudioElement
            audioElement.preload = 'auto'
          }
          break

        case 'image':
          element = document.createElement('img')
          // 设置图片跨域属性（如果需要）
          if (url.startsWith('http') && !url.startsWith(window.location.origin)) {
            const imageElement = element as HTMLImageElement
            imageElement.crossOrigin = 'anonymous'
          }
          break

        case '3d':
        case 'unknown':
          // 对于3D资源和未知类型，使用fetch进行预加载
          fetch(url, {
            method: 'GET',
            // 添加缓存控制
            cache: 'force-cache'
          })
            .then((response) => {
              if (response.ok) {
                return response.blob()
              }
              throw new Error(`HTTP ${response.status}`)
            })
            .then((blob) => {
              if (!isHandled) {
                isHandled = true
                // 创建blob URL供后续使用
                const blobUrl = URL.createObjectURL(blob)
                resources[url] = blobUrl
                handleProgress()
              }
            })
            .catch((error) => {
              if (!isHandled) {
                isHandled = true
                console.warn(`${resourceType === '3d' ? '3D资源' : '未知类型资源'}预加载失败: ${url}`, error)
                handleProgress()
              }
            })

          // 设置超时处理
          setTimeout(() => {
            if (!isHandled) {
              console.warn(`${resourceType === '3d' ? '3D资源' : '未知类型资源'}预加载超时: ${url}`)
              isHandled = true
              handleProgress()
            }
          }, mobile ? 15000 : 20000)

          return // 早返回，不执行下面的element逻辑

        default:
          // 默认情况，不做处理
          console.warn(`未知资源类型: ${resourceType} for ${url}`)
          handleProgress()
          return
      }

      if (element) {
        let cleanupFunction: (() => void) | null = null

        // 处理加载完成
        const handleLoadComplete = () => {
          if (isHandled) return
          isHandled = true
          handleProgress()
          if (cleanupFunction) cleanupFunction()
        }

        // 处理加载错误
        const handleLoadError = () => {
          if (isHandled) return
          isHandled = true
          console.warn(`${resourceType}预加载失败: ${url}`)
          handleProgress()
          if (cleanupFunction) cleanupFunction()
        }

        // 清理事件监听器
        cleanupFunction = () => {
          if (resourceType === 'video') {
            element!.removeEventListener('canplaythrough', handleLoadComplete)
            element!.removeEventListener('loadeddata', handleLoadComplete)
          } else if (resourceType === 'audio') {
            element!.removeEventListener('canplaythrough', handleLoadComplete)
            element!.removeEventListener('loadeddata', handleLoadComplete)
          } else if (resourceType === 'image') {
            element!.removeEventListener('load', handleLoadComplete)
          }
          element!.removeEventListener('error', handleLoadError)
        }

        // 根据资源类型添加事件监听器
        if (resourceType === 'video' || resourceType === 'audio') {
          element.addEventListener('canplaythrough', handleLoadComplete)
          // 移动端添加备用事件监听
          if (mobile) {
            element.addEventListener('loadeddata', handleLoadComplete)
          }
        } else if (resourceType === 'image') {
          element.addEventListener('load', handleLoadComplete)
        }

        // 监听错误事件
        element.addEventListener('error', handleLoadError)

        // 设置超时处理，防止某些资源一直不触发事件
        setTimeout(() => {
          if (!isHandled) {
            console.warn(`${resourceType}预加载超时: ${url}`)
            handleLoadError()
          }
        }, mobile ? 10000 : 15000) // 移动端超时时间更短

        // 设置资源URL开始加载
        if (resourceType === 'video' || resourceType === 'audio') {
          (element as HTMLVideoElement | HTMLAudioElement).src = url
        } else if (resourceType === 'image') {
          (element as HTMLImageElement).src = url
        }

        resources[url] = element
      }
    })

    return resources
  }

  // 分类资源预加载器
  const preloadResourcesByType = (
    urls: string[],
    options: {
      onProgress?: (progress: number, type: string) => void,
      onTypeComplete?: (type: string, resources: any[]) => void
    } = {}
  ) => {
    // 按类型分组资源
    const resourceGroups = {
      video: [] as string[],
      audio: [] as string[],
      image: [] as string[],
      '3d': [] as string[],
      unknown: [] as string[]
    }

    urls.forEach((url) => {
      const type = getResourceType(url)
      resourceGroups[type].push(url)
    })

    const allResources: Record<string, any> = {}
    let completedTypes = 0
    const totalTypes = Object.keys(resourceGroups).filter((key) => resourceGroups[key].length > 0).length

    // 为每种类型单独处理预加载
    Object.entries(resourceGroups).forEach(([type, typeUrls]) => {
      if (typeUrls.length === 0) return

      const typeResources = resourcePreloader(typeUrls, {
        onProgress: (progress) => {
          options.onProgress?.(progress, type)
        }
      })

      // 监听该类型完成
      const checkCompletion = () => {
        const totalTypeResources = typeUrls.length
        const loadedTypeResources = Object.keys(typeResources).length

        if (loadedTypeResources === totalTypeResources) {
          completedTypes += 1
          options.onTypeComplete?.(type, Object.values(typeResources))

          // 合并到总资源中
          Object.assign(allResources, typeResources)
        }
      }

      // 定期检查完成状态
      const interval = setInterval(() => {
        checkCompletion()
        if (completedTypes === totalTypes) {
          clearInterval(interval)
        }
      }, 100)
    })

    return allResources
  }

  // 保持向后兼容的视频预加载器
  const videoPreloader = (urls: string[], { onProgress }: { onProgress?: (progress: number) => void } = {}) => {
    // 过滤出视频资源
    const videoUrls = urls.filter((url) => getResourceType(url) === 'video')
    return resourcePreloader(videoUrls, { onProgress })
  }

  // 特定类型的预加载器
  const audioPreloader = (urls: string[], { onProgress }: { onProgress?: (progress: number) => void } = {}) => {
    const audioUrls = urls.filter((url) => getResourceType(url) === 'audio')
    return resourcePreloader(audioUrls, { onProgress })
  }

  const imagePreloader = (urls: string[], { onProgress }: { onProgress?: (progress: number) => void } = {}) => {
    const imageUrls = urls.filter((url) => getResourceType(url) === 'image')
    return resourcePreloader(imageUrls, { onProgress })
  }

  const model3dPreloader = (urls: string[], { onProgress }: { onProgress?: (progress: number) => void } = {}) => {
    const model3dUrls = urls.filter((url) => getResourceType(url) === '3d')
    return resourcePreloader(model3dUrls, { onProgress })
  }

  return {
    resourcePreloader,
    preloadResourcesByType,
    videoPreloader,
    audioPreloader,
    imagePreloader,
    model3dPreloader,
    getResourceType
  }
}

export default usePreloader
