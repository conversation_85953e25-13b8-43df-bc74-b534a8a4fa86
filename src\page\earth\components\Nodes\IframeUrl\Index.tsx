/* eslint-disable @typescript-eslint/no-unused-vars */
import React, {
  FC, useEffect, useMemo, useRef, useState
} from 'react'
import { v4 as uuidv4 } from 'uuid'
import classNames from 'classnames/bind'
import store, { useAppDispatch, useAppSelector } from '@/store'
import { updateData } from '@/page/earth/earthSlice'
import IframeCommunicationSDK from '@/sdk/iframeCommunicationSDK'
import { useUpdateMedals } from '@/page/earth/hooks/useAchievement'
import EventBus from '@/component/EventBus'
import { Events } from '@/component/EventBus/constant'
import _ from 'lodash'
import chatHelpMusic from '@/asset/musics/chat_help.wav'
import AudioManager from '@/util/AudioManager'
import { useLatest } from 'ahooks'
import styles from './index.module.scss'
import { IIframeUrlNode } from './interface'
import { MedalKey } from '../Achievement/constant'
import {
  EARTHQUAKE_MAP, MATERIAL_MAP, SIMULATE_NPC_MAP, SIMULATE_RESULT_MAP,
  ThreeDes
} from './constant'

const cn = classNames.bind(styles)

interface IIframeUrlProps {
  info: IIframeUrlNode
  onNext: () => void
  id: string
}

const IframeUrl: FC<IIframeUrlProps> = ({ info, onNext, id }) => {
  const { data, config: { currentSaveId } } = useAppSelector((state) => state.earth)
  const { simulate, config, books } = data
  const dispatch = useAppDispatch()
  const iframeRef = useRef<HTMLIFrameElement>(null)
  const sdk = useRef<IframeCommunicationSDK>()
  const { updateGrade } = useUpdateMedals()
  const { presume } = useMemo(() => {
    const { hypothesis } = books
    return {
      presume: _.last(hypothesis.presumes || [])
    }
  }, [books])

  const lastSimulate = useLatest(simulate)

  const onChangeData = (newData) => {
    dispatch(updateData(newData))
  }

  const { content, display } = useMemo(() => ({
    content: info?.content,
    display: info?.content?.display
  }), [info])

  useEffect(() => {
    if (id) {
      if (id === 'node_3') {
        EventBus.emit(Events.aiGuide, ThreeDes.volcano)
      } else if (id === 'node_5') {
        EventBus.emit(Events.aiGuide, ThreeDes.mountain)
      } else if (id === 'node_15') {
        EventBus.emit(Events.aiGuide, ThreeDes.experiment1, () => {
          EventBus.emit(Events.aiGuide, ThreeDes.experiment2, () => {
            EventBus.emit(Events.aiGuide, ThreeDes.experiment3)
          })
        })
      }
    }
    return () => {
      AudioManager.getInstance('sfx').stop()
    }
  }, [id])

  // 发送消息，使用 MessageUtils 中定义的类型
  const sendMessage = (key, _data) => {
    sdk.current?.sendToIframe(key, _data)
  }

  useEffect(() => {
    sendMessage('AssumptionDatas', presume)
  }, [JSON.stringify(presume)])

  const onDoSimulate = ({ result, ...rest }) => {
    const newExperiment = {
      id: uuidv4(),
      success: result === 'success',
      question_why: '',
      question_where: '',
      phenomenon: SIMULATE_RESULT_MAP[result],
      params: [
        {
          level: '最外层',
          material: MATERIAL_MAP[rest.layer1],
          thickness: rest.layer1 === 'thin' ? '厚' : '薄',
          move: rest.movable === 'movable_1' ? '可移动' : '固定',
          gap: rest.crack === 'crack_0' ? '是' : '否',
        },
        {
          level: '中间层',
          material: MATERIAL_MAP[rest.layer2],
          thickness: rest.layer1 === 'thin' ? '厚' : '薄',
        },
        {
          level: '最内层',
          material: MATERIAL_MAP[rest.layer3],
          thickness: rest.layer1 === 'thin' ? '厚' : '薄',
        }
      ]
    }
    const newBooks = _.cloneDeep(store.getState().earth.data.books)
    const newCount = result === 'success' ? 0 : lastSimulate.current.errorCount + 1
    onChangeData({
      simulate: {
        ...lastSimulate.current,
        errorCount: newCount
      },
      books: {
        ...newBooks,
        experiment: newBooks.experiment.concat(newExperiment as any)
      }
    })
    if (result === 'success') {
      updateGrade(MedalKey.earth_researcher, { experiment: true })
    } else if ((config.aiLevel === 'low' && newCount >= 5)
      || (config.aiLevel === 'middle' && newCount >= 3)
      || (config.aiLevel === 'high')) {
      EventBus.emit(Events.ShowNotification, {
        content: '遇到问题了，要不要和我聊一聊？',
        duration: 4000,
        music: chatHelpMusic
      })
    }
    // 成功失败都有npc反馈
    EventBus.emit(Events.aiGuide, SIMULATE_NPC_MAP[result])
  }

  const onKeyPerformance = (_data) => {
    const { key } = _data || {}
    switch (key) {
      case 'onEarthquakeStartChat':
        // 开始对话
        EventBus.emit(Events.aiGuide, EARTHQUAKE_MAP[key], () => {
          // 通知3d显示采集按钮
          sendMessage('OnKeyPerformance', { key: 'onEarthquakeShowButton' })
        })
        break
      case 'onEarthquakeCollectWaveData':
        // 3d完成采集按钮，显示消息
        EventBus.emit(Events.aiGuide, EARTHQUAKE_MAP[key], () => {
          // 回调后告诉3d显示图表
          sendMessage('OnKeyPerformance', { key: 'onEarthquakeShowChart' })
          // 并且继续下一条消息
          EventBus.emit(Events.aiGuide, EARTHQUAKE_MAP.onEarthquakeShowChart, () => {
            // 显示下一个图表消息
            EventBus.emit(Events.aiGuide, EARTHQUAKE_MAP.onEarthquakeShowStartCollect, () => {
              sendMessage('OnKeyPerformance', { key: 'onEarthquakeShowStartCollect' })
            })
          })
        })
        break
      case 'onEarthquakePressStartCollect':
        // 结束对话
        EventBus.emit(Events.aiGuide, EARTHQUAKE_MAP[key])
        break
      default:
        break
    }
  }

  useEffect(() => {
    EventBus.on(Events.sendMessage, sendMessage)
    sdk.current = new IframeCommunicationSDK(iframeRef.current!)
    // 使用通用监听器监听所有消息，在函数内部处理不同类型
    const removeListener = sdk.current.listenAllMessages((messageData, raw) => {
      // 根据消息类型处理
      const { type, data: _data } = raw || {}
      // 您可以在这里根据 type 处理不同的消息
      switch (type) {
        case 'PageLoaded':
          sendMessage('AssumptionDatas', presume)
          // 处理页面加载
          break
        case 'onSimulateSettingPanel':
          EventBus.emit(Events.onSimulateSettingPanel, _data)
          // 处理下一步
          break
        case 'ToNext':
          onNext?.()
          // 处理下一步
          break
        case 'CollectClue':
          // 处理收集线索
          EventBus.emit(Events.getClue, {
            title: _data.title,
            des: _data.description,
            img_key: _data.key || '',
            img_url: _data.imageUrl || '',
          })
          break
        case 'CollectClueComplete':
          // 处理完成收集线索
          updateGrade(MedalKey.earth_researcher, { [messageData?.upgradeKey]: true })
          break
        case 'DoSimulate':
          onDoSimulate(_data)
          // 处理执行模拟
          break
        case 'TrackEvent':
          // todo 埋点
          // 处理埋点
          break

        case 'OnKeyPerformance':
          onKeyPerformance(_data)
          break
        case 'ShowGuide':
          EventBus.emit(Events.fingerTip, _data)
          break
        default:
          break
      }
      console.log('Received message:', { type, data: messageData, raw })
    })

    const onDestroy = () => {
      EventBus.off(Events.sendMessage, sendMessage)
      removeListener()
      sdk.current?.destroy()
    }
    return () => {
      onDestroy()
    }
  }, [])

  const src = useMemo(() => `${content.url + (content.url.includes('?') ? '&' : '?')}uid=${currentSaveId}`, [content?.url, currentSaveId])

  return (
    <div className={cn('iframe-url')}>
      <iframe
        ref={iframeRef}
        src={src}
        style={{
          width: display?.width || '100%',
          height: '100%',
          border: display?.showBorder ? '1px solid #eee' : '0px'
        }}
      />
    </div>
  )
}

export default IframeUrl
