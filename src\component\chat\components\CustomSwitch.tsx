import React, { FC, useCallback } from 'react'
import styles from './CustomSwitch.module.scss'

export interface CustomSwitchProps {
  /** 是否选中 */
  checked?: boolean
  /** 变化时的回调 */
  onChange?: (checked: boolean) => void
  /** 是否禁用 */
  disabled?: boolean
  /** 选中时的内容 */
  checkedChildren?: React.ReactNode
  /** 非选中时的内容 */
  unCheckedChildren?: React.ReactNode

  /** 自定义类名 */
  className?: string
}

const CustomSwitch: FC<CustomSwitchProps> = ({
  checked = false,
  onChange,
  disabled = false,
  checkedChildren = '开',
  unCheckedChildren = '关',
  className = ''
}) => {
  const handleClick = useCallback(() => {
    if (disabled) return
    onChange?.(!checked)
  }, [checked, disabled, onChange])

  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (disabled) return
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault()
      onChange?.(!checked)
    }
  }, [checked, disabled, onChange])

  const switchClass = [
    styles.customSwitch,
    checked ? styles.checked : '',
    disabled ? styles.disabled : '',
    className
  ].filter(Boolean).join(' ')

  return (
    <button
      className={switchClass}
      onClick={handleClick}
      onKeyDown={handleKeyDown}
      disabled={disabled}
      role="switch"
      aria-checked={checked}
      tabIndex={disabled ? -1 : 0}
    >
      <span className={styles.handle} />
      <span className={styles.inner}>
        {checked ? checkedChildren : unCheckedChildren}
      </span>
    </button>
  )
}

export default CustomSwitch
