import React from 'react'
import { Navigate, RouteObject } from 'react-router-dom'
import { asyncLoader } from '../util/loader'

const route: RouteObject = {
  path: '/earth',
  element: asyncLoader(() => import('../page/earth')),
  children: [
    {
      path: '',
      index: true,
      element: <Navigate to="home" replace />
    },
    {
      path: 'home',
      element: asyncLoader(() => import('../page/earth/Main')),
      index: true
    }
  ]
}

export default route
