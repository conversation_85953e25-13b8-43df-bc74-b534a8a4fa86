import React, {
  useMemo, useState, useEffect, useRef
} from 'react'
import classNames from 'classnames/bind'
import { Popover } from 'fish'
import { useAchievement } from '@/page/earth/hooks/useAchievement'
import EventBus from '@/component/EventBus'
import { Events } from '@/component/EventBus/constant'
import scorePng from '@/asset/images/achievement/score.png'
import medalsPng from '@/asset/images/achievement/medals.png'
import popBgPng from '@/asset/images/pop_bg.png'
import backPng from '@/asset/images/achievement/back.png'
import styles from './index.module.scss'
import {
  LEVEL_MEDALS, LEVEL_MEDALS_ICONS, MEDAL_LIST, MedalKey
} from './constant'

const cn = classNames.bind(styles)

const Achievement = () => {
  const {
    scores, totalScore, medals, unlockedMedalCount
  } = useAchievement()
  const [showDesKey, setShowDesKey] = useState<string | null>(null)
  const { currentMedal, currentMedalData } = useMemo(() => ({
    currentMedal: MEDAL_LIST.find((item) => item.key === showDesKey),
    currentMedalData: medals.find((m) => m.key === showDesKey)
  }), [showDesKey, medals])

  const earthResearcherMedal = medals.find((item) => item.key === MedalKey.earth_researcher)
  const otherMedals = medals.filter((item) => item.key !== MedalKey.earth_researcher)

  // 用于跟踪上一次的 otherMedals 的 unlocked 状态
  const prevOtherMedalsRef = useRef<Record<string, boolean>>({})
  // 用于跟踪上一次的 level 值
  const prevLevelRef = useRef<number | null>(null)
  // 用于跟踪上一次的 totalScore 值
  const prevScoreRef = useRef<number | null>(null)

  const level = useMemo(() => {
    if (earthResearcherMedal) {
      let count = 0
      if (earthResearcherMedal.upgrade) {
        const { volcano, mountain, ...rest } = earthResearcherMedal.upgrade
        if (volcano && mountain) {
          count += 1
        }
        Object.keys(rest).forEach((key) => {
          if (key !== 'undefined' && rest[key]) {
            count += 1
          }
        })
      }
      return count
    }
    return 0
  }, [earthResearcherMedal])

  // 处理新勋章的函数
  const handleNewMedal = (medalKey: MedalKey) => {
    console.log('New medal unlocked:', medalKey)
    EventBus.emit(Events.onLottie, { key: 'two', level, medal: medalKey })
    // 在这里添加你的处理逻辑
  }

  // 处理level变化的函数
  const handleLevelChange = (newLevel: number, prevLevel: number) => {
    console.log('Level changed:', { prevLevel, newLevel })
    EventBus.emit(Events.onLottie, { key: 'one', level: newLevel })
    // 在这里添加你的处理逻辑
  }

  // 处理score变化的函数
  const handleScoreChange = (newScore: number) => {
    console.log('Score changed:', newScore)
    EventBus.emit(Events.onLottie, { key: 'get' })
    // 在这里添加你的处理逻辑
  }

  // 监听 otherMedals 的 unlocked 状态变化
  useEffect(() => {
    const prevMedalsStatus = prevOtherMedalsRef.current

    // 检查每个勋章的 unlocked 状态变化
    otherMedals.forEach((medal) => {
      const prevUnlocked = prevMedalsStatus[medal.key]
      const currentUnlocked = medal.unlocked

      // 如果上次是 false，这次是 true，则触发函数
      if (prevUnlocked === false && currentUnlocked === true) {
        handleNewMedal(medal.key)
      }
    })

    // 更新引用，记录当前所有勋章的 unlocked 状态
    const newMedalsStatus: Record<string, boolean> = {}
    otherMedals.forEach((medal) => {
      newMedalsStatus[medal.key] = medal.unlocked
    })
    prevOtherMedalsRef.current = newMedalsStatus
  }, [otherMedals])

  // 监听 level 变化
  useEffect(() => {
    const prevLevel = prevLevelRef.current

    // 如果上次有值且这次值不同，则触发函数
    if (prevLevel !== null && prevLevel !== level) {
      handleLevelChange(level, prevLevel)
    }

    // 更新引用
    prevLevelRef.current = level
  }, [level])

  // 监听 score 变化
  useEffect(() => {
    const prevScore = prevScoreRef.current

    // 如果上次有值且这次值不同，则触发函数
    if (prevScore !== null && totalScore > prevScore) {
      handleScoreChange(totalScore)
    }

    // 更新引用
    prevScoreRef.current = totalScore
  }, [totalScore])

  const renderLevelPng = () => {
    if (earthResearcherMedal) {
      return LEVEL_MEDALS[level]
    }
    return undefined
  }

  const renderDesLevelPng = () => {
    if (earthResearcherMedal) {
      if (level === 0) {
        return LEVEL_MEDALS_ICONS.disabled[0]
      }
      return LEVEL_MEDALS_ICONS.default[level - 1]
    }
    return undefined
  }

  return (
    <div className={cn('achievement')}>
      <div className={cn('top-icons')}>
        <Popover
          placement="bottomLeft"
          title={false}
          overlayClassName={cn('popover')}
          trigger="click"
          content={(
            <div className={cn('score-list')}>
              <img src={popBgPng} className={cn('score-background')} />
              <div className={cn('score-content')}>
                <div className={cn('score-title-sub')}>地球研究积分</div>
                <div className={cn('score-header')}>
                  <span className={cn('name')}>
                    任务
                  </span>
                  <span className={cn('count')}>完成数量</span>
                  <span className={cn('score-count')}>得分</span>
                </div>
                {
                  scores.map((item) => (
                    <div className={cn('score-item')} key={item.key}>
                      <span className={cn('name')}>
                        {item.name}
                        <div className={cn('description')}>{item.description}</div>
                      </span>
                      <span className={cn('count')}>{item.count}</span>
                      <span className={cn('score-count')}>{`+${item.count}`}</span>
                    </div>
                  ))
                }
              </div>
            </div>
          )}
        >
          <div className={cn('score-icon')} id="score-icon">
            <img src={scorePng} alt="积分图标" className={cn('icon')} />
            <span className={cn('count')}>{totalScore}</span>
          </div>
        </Popover>
        <Popover
          placement="bottom"
          title={false}
          overlayClassName={cn('popover')}
          trigger="click"
          onVisibleChange={(v) => {
            if (!v) {
              setShowDesKey(null)
            }
          }}
          content={(
            <div className={cn('medal-list')}>
              <img src={popBgPng} className={cn('medal-background')} />
              <div className={cn('medal-content')}>
                <div className={cn('medal-level-title')}>成长勋章</div>
                <div
                  className={cn('medal-level-item')}
                  onClick={() => {
                    setShowDesKey(MedalKey.earth_researcher)
                  }}
                >
                  <img src={earthResearcherMedal?.unlocked ? renderLevelPng() : earthResearcherMedal?.dis_icon} />
                </div>
                <div className={cn('medal-title')}>勋章</div>
                <div className={cn('medal-items')}>
                  {
                    otherMedals.map((item) => (
                      <div
                        className={cn('medal-item')}
                        key={item.key}
                        onClick={() => {
                          setShowDesKey(item.key)
                        }}
                      >
                        <img src={item?.unlocked ? item.default_icon : item.dis_icon} className={cn('medal-icon')} />
                        <div className={cn('name')}>
                          {item.name}
                        </div>
                      </div>
                    ))
                  }
                </div>
              </div>
              {
                showDesKey && (
                  showDesKey === MedalKey.earth_researcher ? (
                    <div className={cn('medal-des', 'level')}>
                      <div className={cn('close-icon')} onClick={() => setShowDesKey(null)}>
                        <img src={backPng} />
                      </div>
                      <div className={cn('icon')}>
                        <img src={renderDesLevelPng()} />
                        <div>{earthResearcherMedal?.name}</div>
                        <div className={cn('level-key')}>{`LV${level}`}</div>
                        <div className={cn('level-des')}>
                          {
                            LEVEL_MEDALS_ICONS.disabled.map((item, index) => {
                              if (index < level) {
                                return <img key={item} src={LEVEL_MEDALS_ICONS.default[index]} className={cn({ current: index === level - 1 })} />
                              }
                              return (
                                <img key={item} src={item} />
                              )
                            })
                          }
                        </div>
                      </div>
                      <pre className={cn('description', 'level')}>
                        {earthResearcherMedal?.description}
                      </pre>
                    </div>
                  )
                    : (
                      <div className={cn('medal-des')}>
                        <div className={cn('close-icon')} onClick={() => setShowDesKey(null)}>
                          <img src={backPng} />
                        </div>
                        <div className={cn('icon')}>
                          <img src={currentMedalData?.unlocked ? currentMedalData?.default_icon : currentMedalData?.dis_icon} />
                          <div>{currentMedalData?.name}</div>
                        </div>
                        <pre className={cn('description')}>
                          {currentMedal?.description}
                        </pre>
                      </div>
                    )
                )
              }
            </div>
          )}
        >
          <div className={cn('medal-icon')}>
            <img src={medalsPng} alt="勋章图标" className={cn('icon')} />
            <span className={cn('count')}>{unlockedMedalCount}</span>
          </div>
        </Popover>
      </div>
    </div>
  )
}

export default Achievement
