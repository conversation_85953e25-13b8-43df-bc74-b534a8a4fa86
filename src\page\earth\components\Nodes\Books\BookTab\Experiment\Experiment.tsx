/* eslint-disable no-nested-ternary */
import React, {
  FC, useEffect, useMemo, useState
} from 'react'
import classNames from 'classnames/bind'
import { Input, Table } from 'fish'
import { IEarthData } from '@/page/earth/interface'
import sourceHeaderBgPng from '@/asset/images/books/hypothesis/source_imgs/source_header_bg.png'
import experimentalResult from '@/asset/images/books/experiment/experimental_result.png'
import experimentalReflection from '@/asset/images/books/experiment/experimental_reflection.png'
import headerBottom from '@/asset/images/books/hypothesis/target_imgs/header_bottom.png'
import rectangleFail from '@/asset/images/books/experiment/Rectangle_fail.png'
import titleBottom from '@/asset/images/books/experiment/experiment_title_bottom.png'
import divider from '@/asset/images/books/experiment/divider.png'
import rectangleSuccess from '@/asset/images/books/experiment/Rectangle_success.png'
import why from '@/asset/images/books/experiment/reflection_why.png'
import where from '@/asset/images/books/experiment/reflection_where.png'
import inputIcon from '@/asset/images/books/experiment/reflection_input_icon.png'
import _ from 'lodash'
import styles from './index.module.scss'

const cn = classNames.bind(styles)

interface IExperimentProps {
  data: IEarthData['books']['experiment']
  onChange: (data) => void
}

export const ExperimentItem = ({ data }) => {
  const columns = [
    {
      title: '层级',
      dataIndex: 'level',
      key: 'level',
      width: '80px'
    },
    {
      title: '材质',
      dataIndex: 'material',
      key: 'material',
      width: '140px'
    },
    {
      title: '厚度',
      dataIndex: 'thickness',
      key: 'thickness',
      width: '40px',
    },
    {
      title: '移动',
      dataIndex: 'move',
      key: 'move',
      width: '80px',
      render: (v) => (v || '-')
    },
    {
      title: '缝隙',
      dataIndex: 'gap',
      key: 'gap',
      width: '40px',
      render: (v) => (v || '-')
    }
  ]

  return (
    <div className={cn('experiment-item')}>
      {!_.isNil(data?.success) && (
        <img src={data.success ? rectangleSuccess : rectangleFail} className={cn('result')} />
      )}
      <div className={cn('item-group')}>
        <div className={cn('item-title')}>
          <span>实验现象</span>
          <img src={titleBottom} />
        </div>
        <div className={cn('item-subtitle')}>
          {data?.phenomenon}
        </div>
      </div>
      <img src={divider} />
      <div className={cn('item-group')}>
        <div className={cn('item-title')}>
          <span>实验参数</span>
          <img src={titleBottom} />
        </div>
        <Table
          rowKey="id"
          dataSource={data?.params || []}
          columns={columns}
          className={cn('table')}
          pagination={false}
          bordered
        />
      </div>
    </div>
  )
}

const Experiment: FC<IExperimentProps> = ({
  data = [],
  onChange
}) => {
  const [activeKey, setActiveKey] = useState('')

  const { current, reverseList } = useMemo(() => ({
    current: data.find((item) => item.id === activeKey),
    reverseList: _.reverse(data.map((item, index) => ({ ...item, index })))
  }), [data, activeKey])

  const onChangeData = (newValue) => {
    const newData = data.map((item) => {
      if (item.id === current?.id) {
        return {
          ...item,
          ...newValue
        }
      }
      return item
    })
    onChange(newData)
  }

  useEffect(() => {
    if (!activeKey && data.length) {
      setActiveKey(data?.[0]?.id)
    }
  }, [data, activeKey])

  return (
    <div className={cn('experiment')}>
      <div className={cn('experiment-list')}>
        <div className={cn('experiment-title-box')}>
          <span className={cn('experiment-title')}>
            实验结果
            <img className={cn('experiment-key')} src={experimentalResult} />
          </span>
          <span className={cn('experiment-des')}>
            <img src={sourceHeaderBgPng} />
          </span>
        </div>
        <div className={cn('experiment-content')}>
          <div className={cn('tab-list')}>
            {
              reverseList.map((item, index) => (
                <div
                  key={item.id}
                  className={cn('tab-item', { first: index === 0 }, { active: activeKey === item.id })}
                  onClick={() => setActiveKey(item.id)}
                  style={{
                    left: `${-index}rem`,
                    zIndex: reverseList.length - index + 1
                  }}
                >
                  {`实验${item.index + 1}`}
                </div>
              ))
            }
            <img src={headerBottom} className={cn('header-bottom')} />
          </div>
          {current && <ExperimentItem data={current} />}
        </div>
      </div>
      <div className={cn('experiment-form')}>
        <div className={cn('experiment-title-box')}>
          <span className={cn('experiment-title')}>
            实验反思
            <img className={cn('experiment-key')} src={experimentalReflection} />
          </span>
          <span className={cn('experiment-des')}>
            <img src={sourceHeaderBgPng} />
          </span>
        </div>
        <div className={cn('experiment-from-content')}>
          <div className={cn('block-w')} id="block-why">
            <img src={why} className={cn('w-img')} />
            <Input.TextArea
              rows={7}
              key={current?.question_why}
              defaultValue={current?.question_why}
            />
            <img
              src={inputIcon}
              className={cn('w-icon')}
              onClick={(e) => {
                e.stopPropagation()
                const inputElement = e.currentTarget.closest('#block-why')?.querySelector('textarea')
                const currentValue = inputElement?.value || ''
                onChangeData({ question_why: currentValue })
              }}
            />
          </div>
          {!current?.success && (
            <div className={cn('block-w')} id="block-where">
              <img src={where} className={cn('w-img')} />
              <Input.TextArea
                rows={7}
                defaultValue={current?.question_where}
                key={current?.question_where}
              />
              <img
                src={inputIcon}
                className={cn('w-icon')}
                onClick={(e) => {
                  e.stopPropagation()
                  const inputElement = e.currentTarget.closest('#block-where')?.querySelector('textarea')
                  const currentValue = inputElement?.value || ''
                  onChangeData({ question_where: currentValue })
                }}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default Experiment
