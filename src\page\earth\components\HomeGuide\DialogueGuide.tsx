import React, {
  useEffect, useState, useRef
} from 'react'
import classNames from 'classnames/bind'
import EventBus from '@/component/EventBus'
import { Events } from '@/component/EventBus/constant'
import dialogueBg from '@/asset/images/guide/dialogue_text.png'
import AudioManager from '@/util/AudioManager'
import styles from './index.module.scss'

const cn = classNames.bind(styles)

const DialogueGuide = () => {
  const [dialogue, setDialogue] = useState('')
  const [show, setShow] = useState(false)
  const currentCallbackRef = useRef<(() => void) | null>(null)

  const onClose = () => {
    setShow(false)
    setDialogue('')
    AudioManager.getInstance('sfx').stop()
    EventBus.emit(Events.aiGuide, null)
    if (currentCallbackRef.current) {
      currentCallbackRef.current?.()
    }
  }

  const onAiGuide = (_data, callback) => {
    if (_data) {
      setDialogue(_data.dialogue)
      if (_data.music) {
        AudioManager.getInstance('sfx').play(_data.music)
      }
      setShow(true)
      currentCallbackRef.current = callback
    }
  }

  useEffect(() => {
    EventBus.on(Events.aiGuide, onAiGuide)

    return () => {
      EventBus.off(Events.aiGuide, onAiGuide)
    }
  }, [])

  if (!show) {
    return <> </>
  }

  return (
    <div className={cn('dialogue')} onClick={onClose}>
      <img src={dialogueBg} />
      <div className={cn('dialogue-content')}>
        {dialogue}
      </div>
    </div>
  )
}

export default DialogueGuide
