/* eslint-disable @typescript-eslint/no-unused-vars */
import React, { FC } from 'react'
import classNames from 'classnames/bind'
import { useAppDispatch, useAppSelector } from '@/store'
import { updateData } from '@/page/earth/earthSlice'
import styles from './index.module.scss'
import { IOpenUrlNode } from './interface'

const cn = classNames.bind(styles)

interface IOpenUrlProps {
  info: IOpenUrlNode
}

const OpenUrl: FC<IOpenUrlProps> = ({ info }) => {
  const { data } = useAppSelector((state) => state.earth)
  const dispatch = useAppDispatch()

  const onChangeData = (newData) => {
    dispatch(updateData(newData))
  }

  return (
    <div className={cn('nodes')}>
      test
    </div>
  )
}

export default OpenUrl
