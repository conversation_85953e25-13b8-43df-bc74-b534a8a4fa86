/**
  * 相关参数文档请参考
  * https://doc.gem-mine.tech/#/zh-cn/toolkit/api/gms
  */
const MomentLocalesPlugin = require('moment-locales-webpack-plugin')

const { proxyConfig } = require('./src/config/request/proxy')

module.exports = {
  publicPath: './',
  /**
   * 本地开发时，需要代理转发的请求
   */
  devServer: {
    proxy: proxyConfig
  },
  // @ts-ignore
  cdn: true, // 是否开启CDN。gif转换库worker需要同域
  // @ts-ignore
  cdnEnv: 'product', // `keyof cdnConfig`
  cdnConfig: {
    // @ts-ignore
    product: {
      host: 'https://cs.101.com', // 域名
      bucket: 'aic_earth_research', // 内容服务上对应的服务名
      path: `build/${+new Date()}`, // 路径
      resolveHost: 'https://gcdncs.101.com', // publicPath中设置的requestHost值
    },
  },
  chainWebpack(config) {
    // use webpack-chain
    config
      .plugin('define')
      .tap((args) => {
        let sdpEnv
        if (process.env.BUILD_ON_SDP) {
          sdpEnv = 'window.__global_env'
        } else {
          sdpEnv = JSON.stringify(process.env.SDP_ENV)
        }
        args[0]['process.env'].SDP_ENV = sdpEnv
        return args
      })
  },
  configureWebpack: {
    // normal webpack config
    resolve: {
      alias: {
        'uc-selector': '@sdp.nd/uc-selector',
        one: '@sdp.nd/one',
        'cloud-office-util': '@sdp.nd/cloud-office-util',
      },
      extensions: [
        '.css',
        '.less',
        '.sass',
        '.scss'
      ]
    },
    plugins: [
      new MomentLocalesPlugin({
        localesToKeep: ['zh-cn']
      }),
    ]
  }
}
