declare namespace NsNdr {
  // Ndr初始化参数
  export interface IConfig {
    env: string
    tenant_id: string
    container_id: string
    app_id: string
    origins: Partial<NsNdr.IOrigins>
  }

  // Ndr初始化origins参数
  export interface IOrigins {
    NDR_GATEWAY: string
    CS: string
  }

  // getResourceThumbUrl对应的option
  export interface IResourceThumbOption {
    size?: string
    csHost?: string
  }

  // resource 参数
  export interface IResourceOption {
    resource_id: string
    include?: string
    global_lang?: string
    ti_file_flag?: string
    version?: string
    tenant_id?: string
    container_id?: string
    attachment?: boolean
  }

  // resource实体
  export interface IResource {
    id: string
    global_title: Record<string, string>
    global_description?: Record<string, string>
    language: string
    resource_type_code: string
    tags?: string[]
    [x: string]: any
  }

  // 标签视图实体
  export interface ITagView {
    code: string
    global_description: Record<string, unknown>
  }

  // 标签视图参数
  export interface ITagViewOption {
    code: string
    global_lang?: string
    tenant_id?: string
    container_id?: string
  }

  export interface IUpdateResource {
    resource_id: string,
    resource: Partial<IResource>
  }

  export interface IUpdateResourceStatus {
    resource_id: string,
    value: string
  }

  export interface IResourceUrl {
    source: {
      host: string
      url: string
      [x: string]: any
    }
    [ti_flag: string]: {
      host: string
      url: string
      [x: string]: any
    }
  }

  // getChapterInfo 参数
  export interface IGetChapterInfoArgs {
    tree_node_id: string
    tenant_id?: string
    container_id?: string
  }

  export interface IChapterInfo {
    /**
     * 章节id
     */
    id: string
    /**
     * 章节名称
     */
    global_title: Record<string, string>
    /**
     * 章节富文本名称
     */
    global_rich_title: Record<string, string>
    /**
     * 章节路径
     */
    node_path: string
    /**
     * 章节描述
     */
    description: string
    /**
     * 教材id
     */
    tree_id: string
    /**
     * 父章节id，若章节是根节点，则此值为root
     */
    parent: string
    language: string
    custom_properties: any,
    create_time: string
    update_time: string
  }

  export interface ITagCascadesOption {
    tag_path: string
  }

  export interface ITagListOption {
    tag_dimension_id: string
    tag_name?: string
    $limit?: number
    $offset?: number
  }

  export interface ITagDetail {
    global_tag_name: {
      [lang: string]: string
    }
    id: string
  }

  export interface ITag {
    tag_path: string // 标签视图code
    hierarchies: [{
      children: {
        tag_id: string // 标签id
        global_tag_name: { // 标签名称
          [lang: string]: string
        }
        hierarchies: ITag['hierarchies']
        ext: {
          has_next_tag_path: [], // 下一层级联的标签ID列表，可用于判断是否存在下一层级联
          tag_dimension_id: string
        } | null
      }[]
      ext: {} // 用户自定义属性
      global_hierarchy_name: { // 视图层级名称
        [lang: string]: string
      }
    }] | null
  }

  export interface IDataDictionary {
    item_code: string
    item_global_name: {
      [lang: string]: string
    }
  }
}

declare class NDR {
  constructor(config: Partial<NsNdr.IConfig>);

  static setGlobalEnv: (env: string) => void

  static getResourceThumbUrl: (
    resource: NsNdr.IResource, options?: NsNdr.IResourceThumbOption
  ) => string

  static getAccessibleUrl: (
    sourceUrl: string, options?: { env: string }
  ) => string

  config: NsNdr.IConfig
  setAccessToken(token: string): void
  // eslint-disable-next-line @typescript-eslint/ban-types
  setCustomGetAuthHeader(getAuthHeaderAsync: Function): void
  queryResources(option: any): Promise<IListResponse<NsNdr.IResource>>
  queryOnlineResources(option: any): Promise<IListResponse<NsNdr.IResource>>
  getResource(option: NsNdr.IResourceOption): Promise<NsNdr.IResource>
  batchGetResources(option: {
    resources: NsNdr.IResourceOption[], include: string
  }): Promise<NsNdr.IResource[]>
  getChapterInfo(option: NsNdr.IGetChapterInfoArgs): Promise<NsNdr.IChapterInfo>
  getResourceUrl(option: NsNdr.IResourceOption): Promise<NsNdr.IResourceUrl>
  getTagView(option: NsNdr.ITagViewOption): Promise<NsNdr.ITagView>
  generateAccessToken(): Promise<string>
  updateResource(option: NsNdr.IUpdateResource): Promise<void>
  updateResourceStatus(option: NsNdr.IUpdateResourceStatus): Promise<void>
  getTagCascades(option: NsNdr.ITagCascadesOption): Promise<NsNdr.ITag>
  getTagList(option: NsNdr.ITagListOption): Promise<IListResponse<NsNdr.ITagDetail>>
  getUploadUrl(option: any): Promise<any>
  createResource(data: any): Promise<any>
  addResourceTags(data: Partial<NsNdr.IResource>): Promise<any>
  getCsToken(option: any): Promise<any>
}
