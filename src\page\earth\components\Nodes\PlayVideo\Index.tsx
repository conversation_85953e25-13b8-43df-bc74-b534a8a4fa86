import React, {
  FC, useEffect, useMemo, useRef
} from 'react'
import classNames from 'classnames/bind'
import EventBus from '@/component/EventBus'
import { Events } from '@/component/EventBus/constant'
import store from '@/store'
import useTTS from '@/component/chat/hooks/useTts'
import { getTtsToken } from '@/page/earth/network'
import { useLatest } from 'ahooks'
import styles from './index.module.scss'
import { IPlayVideoNode } from './interface'

const cn = classNames.bind(styles)

interface IPlayVideoProps {
  info: IPlayVideoNode
  onEnd: () => void
  guided: boolean
}

const PlayVideo: FC<IPlayVideoProps> = ({ guided, info, onEnd }) => {
  const videoRef = useRef<HTMLVideoElement>(null)
  // TTS 相关
  const ttsHook = useTTS()
  const {
    getIsConnected,
    startTTS,
    sendText: sendTTSText,
    stopTTS,
    finishSession
  } = ttsHook

  const { content, playback } = useMemo(() => ({
    content: info?.content,
    playback: info?.content?.playback,
  }), [info])

  const lastNeed = useLatest(info.need_tts)
  const lastVoiceType = useLatest(info.voice_type)

  const playTts = async (text: string) => {
    const res = await getTtsToken({
      license_key: 'EkQHCrPMuFp2znxMtb5A' // 固定值
    })

    let newText = text
    await startTTS({
      appid: res.tts.appid,
      stsToken: res.tts.sts_token,
      speaker: lastVoiceType.current || res.tts.voice_type,
      resourceId: 'volc.service_type.10029',
      onPlayComplete: ({ text: val, end }) => {
        if (end) {
          videoRef.current?.pause()
          return
        }
        if (val) {
          newText = newText.replace(val, '')
        }
        if (!newText.trim()) {
          finishSession()
        }
      }
    })
    if (getIsConnected()) {
      sendTTSText(text)
      setTimeout(() => {
        sendTTSText(text)
      }, 500)
    }
  }
  useEffect(() => {
    if (lastNeed.current) {
      const { conclusion_value } = store.getState()?.earth?.data?.books?.conclusion || {}
      if (conclusion_value) {
        playTts(conclusion_value)
      }
    }
  }, [lastNeed.current])

  useEffect(() => {
    if (!videoRef.current) return

    // 监听播放完成事件
    const handleEnded = () => {
      if (lastNeed.current) {
        stopTTS()
      }
      onEnd()
    }
    videoRef.current.addEventListener('ended', handleEnded)
    EventBus.on(Events.GuideHome, () => {
      videoRef.current?.play()
    })
    // 组件卸载时移除监听
    return () => {
      EventBus.off(Events.GuideHome)
      videoRef.current?.removeEventListener('ended', handleEnded)
    }
  }, [])

  return (
    <div className={cn('play-video')}>
      <video
        ref={videoRef}
        src={content?.url}
        poster={content?.posterUrl}
        autoPlay={guided && playback?.autoPlay}
        muted={playback?.muted}
        loop={playback?.loop}
        controls={playback?.showControls}
        playsInline
        webkit-playsinline="true"
        key={info.id}
      />
    </div>
  )
}

export default PlayVideo
