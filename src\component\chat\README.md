# AI对话组件使用文档

## 功能概述

这是一个功能完整的AI对话组件，**专为外部数据管理设计**，支持：

### 一、智能浮窗模块（Smart Floating Widget）
- ✅ 可拖拽头像，使用react-draggable实现
- ✅ 保存最后位置，支持返回给调用方
- ✅ 点击头像切换弹窗显隐
- ✅ 暴露openChat()方法供外部调用

### 二、对话核心模块（Chat Core）
- ✅ 历史对话列表，使用react-virtualized实现双向虚拟滚动
- ✅ 按会话ID分组的历史记录
- ✅ **完全依赖外部传入的历史对话数据**
- ✅ 通过回调函数通知外部数据变化

### 三、消息处理系统（Message Engine）
- ✅ SSE（Server-Sent Events）流式消息处理
- ✅ 打字机动画效果实时渲染
- ✅ 中途响应中断机制
- ✅ 消息扩展功能：
  - 截图：html2canvas实现消息框截图
  - 点赞：异步更新消息meta数据
  - 语音回放：Web Speech Synthesis API
  - 重生成：带上下文重新请求

### 四、语音交互
- ✅ 集成Web Speech Synthesis API
- ✅ 中断当前播报的优先级逻辑
- ✅ 支持语音配置（语音、语速、音调）

## 基本使用

```tsx
import React, { useRef, useState } from 'react'
import { Chat, IChatRef, IHistoryData } from '@sdp.nd/ai-courseware-component'

const App = () => {
  const chatRef = useRef<IChatRef>(null)
  const [historyData, setHistoryData] = useState<IHistoryData[]>([])

  // 自定义消息发送处理
  const handleSendMessage = async (message: string): Promise<string> => {
    // 调用你的AI API
    const response = await fetch('/api/ai-chat', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ message })
    })
    const data = await response.json()
    return data.reply
  }

  // 处理历史数据变化
  const handleHistoryChange = (newHistory: IHistoryData[]) => {
    setHistoryData(newHistory)
    // 在这里可以保存到你的后端或本地存储
    localStorage.setItem('chat-history', JSON.stringify(newHistory))
  }

  return (
    <div>
      <button onClick={() => chatRef.current?.openChat()}>
        打开AI助手
      </button>
      
      <Chat
        ref={chatRef}
        title="智能AI助手"
        avatar="/avatar.png"
        width={450}
        height={650}
        defaultPosition={{ x: 100, y: 100 }}
        historyData={historyData}
        onHistoryChange={handleHistoryChange}
        onSendMessage={handleSendMessage}
        ttsConfig={{
          enabled: true,
          rate: 1.0,
          pitch: 1.0
        }}
        enableScreenshot={true}
        enableLike={true}
        enableVoice={true}
        enableRegenerate={true}
      />
    </div>
  )
}
```

## 外部数据管理

### 数据结构

```tsx
interface IMessage {
  id: string
  content: string
  role: 'user' | 'assistant'
  timestamp: number
  sessionId: string
  meta?: {
    liked?: boolean
    audio?: string
  }
}

interface IHistoryData {
  sessionId: string
  messages: IMessage[]
}
```

### 数据流

1. **初始化**：通过`historyData`属性传入历史数据
2. **数据变化**：通过`onHistoryChange`回调接收更新
3. **持久化**：由外部调用方负责数据存储

```tsx
// 从后端加载历史数据
useEffect(() => {
  const loadHistory = async () => {
    const response = await fetch('/api/chat/history')
    const data = await response.json()
    setHistoryData(data)
  }
  loadHistory()
}, [])

// 保存历史数据到后端
const handleHistoryChange = async (newHistory: IHistoryData[]) => {
  setHistoryData(newHistory)
  
  // 保存到后端
  await fetch('/api/chat/history', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(newHistory)
  })
}
```

## 高级配置

### SSE流式API使用

如果你的后端支持SSE，不需要传入`onSendMessage`，组件会自动使用内置的SSE处理：

```tsx
<Chat
  apiUrl="/api/chat/stream"
  apiHeaders={{
    'Authorization': 'Bearer your-token',
    'X-Custom-Header': 'value'
  }}
  historyData={historyData}
  onHistoryChange={handleHistoryChange}
  // 其他配置...
/>
```

### 语音配置

```tsx
<Chat
  ttsConfig={{
    enabled: true,
    rate: 0.8,    // 语速 0.1-10
    pitch: 1.2,   // 音调 0-2
    voice: voices[0] // 特定语音
  }}
  historyData={historyData}
  onHistoryChange={handleHistoryChange}
  // 其他配置...
/>
```

## API接口

### IChatProps

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| visible | boolean | false | 控制对话窗口显隐 |
| defaultVisible | boolean | false | 默认显隐状态 |
| onVisibleChange | (visible: boolean) => void | - | 显隐状态变化回调 |
| avatar | string | - | 头像图片URL |
| title | string | 'AI助手' | 对话窗口标题 |
| width | number | 400 | 对话窗口宽度 |
| height | number | 600 | 对话窗口高度 |
| defaultPosition | IPosition | - | 浮窗默认位置 |
| onPositionChange | (position: IPosition) => void | - | 位置变化回调 |
| **historyData** | **IHistoryData[]** | **-** | **外部历史数据（必须）** |
| **onHistoryChange** | **(history: IHistoryData[]) => void** | **-** | **历史数据变化回调（必须）** |
| apiUrl | string | '/api/chat' | SSE API地址 |
| apiHeaders | Record<string, string> | {} | API请求头 |
| onSendMessage | (message: string) => Promise<string> | - | 自定义发送消息处理 |
| ttsConfig | ISpeechConfig | - | 语音配置 |
| enableScreenshot | boolean | true | 启用截图功能 |
| enableLike | boolean | true | 启用点赞功能 |
| enableVoice | boolean | true | 启用语音功能 |
| enableRegenerate | boolean | true | 启用重新生成功能 |

### IChatRef 方法

| 方法 | 类型 | 说明 |
|------|------|------|
| openChat | () => void | 打开对话窗口 |
| closeChat | () => void | 关闭对话窗口 |
| toggleChat | () => void | 切换对话窗口显隐 |
| sendMessage | (message: string) => Promise<void> | 发送消息 |

## 设计理念

1. **外部数据管理**：组件不负责数据持久化，完全依赖外部传入
2. **响应式设计**：支持移动端和桌面端
3. **高性能**：虚拟滚动处理大量消息
4. **可扩展**：模块化设计，易于定制
5. **类型安全**：完整的TypeScript类型支持

## 注意事项

1. **数据管理**：组件不提供内置存储，需要外部管理历史数据
2. **浏览器兼容性**：语音功能需要现代浏览器支持Web Speech API
3. **性能优化**：虚拟滚动已处理大量消息的性能问题
4. **移动端适配**：组件已适配移动端触摸操作
