.start-home {
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  align-items: center;
  height: 100vh;
  background: url("../../../../asset/images/home/<USER>") no-repeat center center;
  background-size: 100% 100%;
  padding: 3.125rem 6.25rem;
  .progress {
    :global {
      .fish-progress-outer {
        padding-right: 0rem;
      }
      .fish-progress-text {
        display: none;
      }
      .fish-progress-bg {
        background: linear-gradient(
          270deg,
          #fffca1 0.02%,
          #ff6f57 40.14%,
          #f15bdf 51.54%,
          #bf75ff 68.38%,
          #8397ff 85.72%,
          #78fbff 99.09%
        );
      }
    }
  }
  .start-button {
    width: 12.5rem;
    visibility: hidden;
    &.visible {
      visibility: visible;
    }
  }
}
