// 消息类型
export interface IMessage {
  id: string
  content: string
  role: 'user' | 'assistant' | 'system'
  timestamp: number
  sessionId: string
  status: 'completed' | 'error' | 'loading'
  meta?: {
    liked?: boolean
    audio?: string
  }
}

// 位置信息
export interface IPosition {
  x: number
  y: number
}

// TTS 配置
export interface ITtsConfig {
  enabled: boolean
  appid: string
  stsToken: string
  speaker: string
  resourceId: string
}

// 主组件Props
export interface IChatProps {
  // 基础配置
  className?: string
  visible?: boolean
  defaultVisible?: boolean
  onVisibleChange?: (visible: boolean) => void

  // 外观配置
  userAvatar?: string
  avatar?: string
  chatInAvatar?: string
  title?: string
  width?: string
  height?: string

  // 位置配置
  position?: IPosition
  defaultPosition?: IPosition
  onPositionChange?: (position: IPosition) => void

  // 消息数据
  messages?: IMessage[]
  onMessagesChange?: (messages: IMessage[]) => void

  // API配置
  apiUrl?: string
  model?: string
  temperature?: number
  apiHeaders?: Record<string, string>
  onSendMessage?: (message: string) => Promise<string>
  onStreamMessage?: (chunk: string, isComplete: boolean) => void

  // 语音配置
  ttsConfig: ITtsConfig
  onTtsConfigChange?: (config: ITtsConfig) => void

  // 扩展功能配置
  enableScreenshot?: boolean
  enableLike?: boolean
  enableVoice?: boolean
  enableRegenerate?: boolean
  onScreenshot?: (messageId: string) => void
  onLike?: (messageId: string) => void
  onVoice?: (messageId: string) => void
  onClick?: () => void
  onClose?: () => void
}

// 智能浮窗Props
export interface IFloatingWidgetProps {
  visible: boolean
  position: IPosition
  avatar?: string
  onPositionChange: (position: IPosition) => void
  onToggleChat: () => void
  onClick?: () => void
  notificationContent: string
}

// 对话窗口Props
export interface IChatWindowProps {
  userAvatar?: string
  avatar?: string
  visible: boolean
  title?: string
  width?: string
  height?: string
  messages: IMessage[]
  isLoading?: boolean
  onClose: () => void
  onSendMessage: (message: string) => void
  onLikeMessage: (messageId: string) => void
  onScreenshotMessage: (messageId: string) => void
  onVoiceMessage: (messageId: string) => void
  onRegenerateMessage: (messageId: string) => void
  onInterruptSpeech: () => void
  ttsConfig?: ITtsConfig
  onTtsConfigChange?: (config: ITtsConfig) => void
  enableScreenshot?: boolean
  enableLike?: boolean
  enableVoice?: boolean
  enableRegenerate?: boolean
}

// 消息项Props
export interface IMessageItemProps {
  message: IMessage
  userAvatar?: string
  avatar?: string
  onLike?: (messageId: string) => void
  onScreenshot?: (messageId: string) => void
  onVoice?: (messageId: string) => void
  onRegenerate?: (messageId: string) => void
  enableScreenshot?: boolean
  enableLike?: boolean
  enableVoice?: boolean
  enableRegenerate?: boolean
  hideOperation?: boolean
}

// SSE事件类型
export interface ISSEEvent {
  type: 'message' | 'error' | 'done'
  data: string
}
