import {
  useState, useCallback, useRef
} from 'react'
import { useControllableValue } from 'ahooks'
import { IMessage, IChatProps, ITtsConfig } from '../interface'
import { utils } from '../utils/helpers'
import useTTS from './useTts'

/**
 * 聊天核心逻辑Hook
 */
const useChat = (props: IChatProps): {
  visible: boolean
  setVisible: (value: boolean) => void
  position: { x: number, y: number }
  setPosition: (value: { x: number, y: number }) => void
  messages: IMessage[]
  setMessages: (messages: IMessage[]) => void
  isLoading: boolean
  sendMessage: (content: string, role?: 'user' | 'system') => Promise<void>
  regenerateMessage: (messageId: string) => Promise<void>
  interruptConversation: () => void
  clearMessages: () => void
  ttsConfig: ITtsConfig
  initializeTTS: () => Promise<void>
  ttsHook: any
} => {
  const {
    apiUrl = 'https://aic-aiproxy.sdp.101.com/v1/chat/completions',
    model = 'deepseek-v3-0324',
    temperature = 0.7,
    apiHeaders = {},
    onSendMessage,
    onStreamMessage,
    ttsConfig
  } = props

  // 可控状态
  const [visible, setVisible] = useControllableValue(props, {
    valuePropName: 'visible',
    trigger: 'onVisibleChange',
    defaultValue: false
  })

  const [position, setPosition] = useControllableValue(props, {
    valuePropName: 'position',
    trigger: 'onPositionChange',
    defaultValuePropName: 'defaultPosition',
    defaultValue: { x: window.innerWidth - 100, y: window.innerHeight - 100 }
  })

  // 内部状态
  const [messages, setMessages] = useControllableValue<IMessage[]>(props, {
    valuePropName: 'messages',
    trigger: 'onMessagesChange',
    defaultValue: []
  })
  const [isLoading, setIsLoading] = useState(false)

  // 服务实例和控制器
  const controllerRef = useRef<AbortController | null>(null)

  // TTS 相关
  const ttsHook = useTTS()
  const {
    getIsConnected,
    startTTS,
    sendText: sendTTSText,
    finishSession: finishTTSSession,
    stopTTS
  } = ttsHook

  // 初始化 TTS 的函数
  const initializeTTS = useCallback(async () => {
    if (ttsConfig?.enabled && ttsConfig.appid && ttsConfig.stsToken) {
      try {
        await startTTS({
          appid: ttsConfig.appid,
          stsToken: ttsConfig.stsToken,
          speaker: ttsConfig.speaker,
          resourceId: ttsConfig.resourceId
        })
        console.info('TTS initialization succeeded')
      } catch (error) {
        console.error('TTS initialization failed:', error)
      }
    }
  }, [ttsConfig, startTTS])

  // 流式处理函数
  const streamResponse = useCallback(async (prompt: string, messageId: string, role = 'user'): Promise<string> => {
    // 创建AbortController用于取消请求
    controllerRef.current = new AbortController()
    const { signal } = controllerRef.current
    stopTTS()
    // 在开始流式处理时初始化TTS
    await initializeTTS()

    try {
      // 准备请求参数
      const requestData = {
        model,
        messages: messages.map((msg) => ({ role: msg.role, content: msg.content })).concat([{ role, content: prompt }]),
        temperature,
        stream: true
      }
      // 发送请求
      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...apiHeaders
        },
        body: JSON.stringify(requestData),
        signal
      })

      if (!response.ok) {
        throw new Error(`API请求失败: ${response.status} ${response.statusText}`)
      }

      // 处理流式响应
      const reader = response.body?.getReader()
      if (!reader) {
        throw new Error('无法获取响应流')
      }

      const decoder = new TextDecoder()
      let accumulatedText = ''

      // eslint-disable-next-line no-constant-condition
      while (true) {
        const { done, value } = await reader.read()

        if (done) {
          break
        }

        // 解码数据
        const chunk = decoder.decode(value, { stream: true })

        // 处理SSE格式
        const lines = chunk.split('\n').filter((line) => line.trim() !== '')

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.substring(6)

            if (data === '[DONE]') {
              // eslint-disable-next-line no-continue
              continue
            }

            try {
              const parsed = JSON.parse(data)
              const content = parsed.choices?.[0]?.delta?.content
              if (content) {
                const newAccumulatedText = accumulatedText + content
                accumulatedText = newAccumulatedText
                // 检查是否形成完整句子，如果是则发送给TTS
                if (ttsConfig?.enabled && getIsConnected()) {
                  sendTTSText(content)
                }

                // 实时更新消息内容
                setMessages((prev) => {
                  const newMessages = [...prev]
                  const targetIndex = newMessages.findIndex((m) => m.id === messageId)
                  if (targetIndex !== -1) {
                    newMessages[targetIndex] = {
                      ...newMessages[targetIndex],
                      content: newAccumulatedText,
                      timestamp: Date.now(),
                      status: 'completed',
                    }
                  }
                  return newMessages
                })

                // 调用流式回调
                if (onStreamMessage) {
                  onStreamMessage(content, false)
                }
              }
            } catch (e) {
              console.error('解析响应失败:', e)
            }
          }
        }
      }

      // 流式完成回调
      if (onStreamMessage) {
        onStreamMessage('', true)
      }

      return accumulatedText
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('请求已取消')
      } else if (error instanceof Error && error.message.includes('Failed to fetch')) {
        throw new Error('网络请求失败')
      } else {
        throw error
      }
    } finally {
      controllerRef.current = null
    }
  }, [apiUrl, model, temperature, apiHeaders,
    onStreamMessage, setMessages, ttsConfig, messages,
    getIsConnected, sendTTSText, finishTTSSession, initializeTTS])

  // 发送消息
  const sendMessage = useCallback(async (content: string, role = 'user') => {
    if (isLoading || content.trim() === '') return
    const userMessage: IMessage = {
      id: utils.generateId(),
      content: content.trim(),
      role,
      status: 'completed', // 初始状态为已完成，后续会更新
      timestamp: Date.now(),
      sessionId: 'current' // 简化为固定值
    }

    // 创建AI响应消息占位符
    const assistantMessageId = utils.generateId()
    const assistantMessage: IMessage = {
      id: assistantMessageId,
      content: '', // 开始时为空，流式填充
      role: 'assistant',
      status: 'loading', // 初始状态为已完成，后续会更新
      timestamp: Date.now(),
      sessionId: 'current'
    }

    // 一次性添加用户消息和AI消息占位符
    setMessages((prev) => [...prev, userMessage, assistantMessage])
    setIsLoading(true)

    try {
      let responseContent = ''

      if (onSendMessage !== undefined) {
        // 使用自定义发送函数
        responseContent = await onSendMessage(content)

        // 更新AI消息内容
        setMessages((prev) => {
          const newMessages = [...prev]
          const targetIndex = newMessages.findIndex((m) => m.id === assistantMessageId)
          if (targetIndex !== -1) {
            newMessages[targetIndex] = {
              ...newMessages[targetIndex],
              content: responseContent,
              timestamp: Date.now(),
              status: 'completed',
            }
          }
          return newMessages
        })
      } else {
        // 使用流式处理
        responseContent = await streamResponse(content, assistantMessageId, role)
      }
    } catch (error) {
      // 更新AI消息为错误内容
      setMessages((prev) => {
        const newMessages = [...prev]
        const targetIndex = newMessages.findIndex((m) => m.id === assistantMessageId)
        if (targetIndex !== -1) {
          newMessages[targetIndex] = {
            ...newMessages[targetIndex],
            content: error instanceof Error ? error.message : String(error),
            status: 'error',
            timestamp: Date.now()
          }
        }
        return newMessages
      })
      console.error('Send message failed:', error)
    } finally {
      setIsLoading(false)
    }
  }, [isLoading, onSendMessage, streamResponse, ttsConfig, setMessages])

  // 重新生成指定的AI回答
  const regenerateMessage = useCallback(async (messageId: string) => {
    if (isLoading) return

    // 找到要重新生成的消息
    const messageIndex = messages.findIndex((m) => m.id === messageId)
    if (messageIndex === -1 || messages[messageIndex].role !== 'assistant') {
      console.warn('Invalid message ID or message is not from assistant')
      return
    }

    // 找到对应的用户消息
    let userMessage: IMessage | null = null
    for (let i = messageIndex - 1; i >= 0; i--) {
      if (messages[i].role === 'user') {
        userMessage = messages[i]
        break
      }
    }

    if (!userMessage) {
      console.warn('Cannot find corresponding user message')
      return
    }

    setIsLoading(true)

    try {
      let responseContent = ''

      if (onSendMessage !== undefined) {
        // 使用自定义发送函数
        responseContent = await onSendMessage(userMessage.content)

        // 更新最终的AI消息
        setMessages((prev) => {
          const newMessages = [...prev]
          const targetIndex = newMessages.findIndex((m) => m.id === messageId)
          if (targetIndex !== -1) {
            newMessages[targetIndex] = {
              ...newMessages[targetIndex],
              content: responseContent,
              timestamp: Date.now(),
              status: 'completed',
            }
          }
          return newMessages
        })
      } else {
        // 使用流式处理重新生成
        responseContent = await streamResponse(userMessage.content, messageId)
      }
    } catch (error) {
      // 更新为错误消息
      setMessages((prev) => {
        const newMessages = [...prev]
        const targetIndex = newMessages.findIndex((m) => m.id === messageId)
        if (targetIndex !== -1) {
          newMessages[targetIndex] = {
            ...newMessages[targetIndex],
            content: error instanceof Error ? error.message : String(error),
            timestamp: Date.now(),
            status: 'error',
          }
        }
        return newMessages
      })
      console.error('Regenerate message failed:', error)
    } finally {
      setIsLoading(false)
    }
  }, [messages, isLoading, onSendMessage, streamResponse, ttsConfig, setMessages])

  // 中断当前对话
  const interruptConversation = useCallback(() => {
    // 中断流式请求
    if (controllerRef.current) {
      controllerRef.current.abort()
      controllerRef.current = null
    }

    // 停止TTS
    if (getIsConnected()) {
      stopTTS()
    }

    setIsLoading(false)
  }, [getIsConnected, stopTTS])

  // 清空消息
  const clearMessages = useCallback(() => {
    setMessages([])
  }, [setMessages])

  return {
    // 状态
    visible,
    setVisible,
    position,
    setPosition,
    messages,
    setMessages,
    isLoading,

    // 方法
    sendMessage,
    regenerateMessage,
    interruptConversation,
    clearMessages,

    // 语音配置
    ttsConfig,
    initializeTTS,
    ttsHook
  }
}

export default useChat
