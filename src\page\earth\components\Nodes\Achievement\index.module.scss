.achievement {
  margin-right: 1.25rem;
  .top-icons {
    display: flex;
    .score-icon,
    .medal-icon {
      position: relative;
      width: 6.5rem;
      height: 2.5rem;
      img {
        width: 100%;
        height: 100%;
        position: absolute;
        left: 0;
        top: 0;
      }
      .count {
        position: absolute;
        left: 50%;
        font-size: 1rem;
        font-weight: bold;
        color: #fff;
        line-height: 2.375rem;
      }
    }
  }
}

.popover {
  padding: 0rem;
  :global {
    .fish-popover-arrow {
      display: none;
    }
    .fish-popover-inner {
      padding: 0;
      background: transparent;
      box-shadow: none;
      .fish-popover-inner-content {
        padding: 0;
      }
    }
  }
}

.score-list {
  position: relative;
  width: 21.625rem;
  height: 26.25rem;
  left: -1.25rem;
  .score-background,
  .score-content {
    width: 100%;
    position: absolute;
    left: 0;
    top: 0;
  }
  .score-content {
    z-index: 2;
    padding: 1.25rem;
    .score-title-sub {
      color: #3d3d3d;
      font-family: "PingFang SC";
      font-size: 1rem;
      font-family: "Alibaba PuHuiTi";
      font-style: normal;
      font-weight: 700;
      line-height: 2.875rem;
    }

    .score-header {
      border-radius: 0.4375rem;
      background: rgba(50, 50, 50, 0.05);
      padding: 0.625rem;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin: 0.625rem 0rem;
      .name {
        width: 9.375rem;
      }
      .count {
        width: 6.25rem;
        text-align: center;
      }
      .score-count {
        width: 4.0625rem;
        text-align: center;
      }
    }
    .score-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 0.875rem;
      font-style: normal;
      font-weight: 400;
      color: #000;
      height: 3.75rem;
      &:not(:last-child) {
        border-bottom: 0.0625rem solid rgba(50, 50, 50, 0.05);
      }
      .name {
        color: #323232;
        font-weight: bold;
        font-size: 1rem;
        width: 11.25rem;
        line-height: 1.5rem;
        .description {
          font-size: 0.875rem;
          color: rgba(50, 50, 50, 0.3);
          font-family: "Alibaba PuHuiTi";
          font-weight: 400;
        }
      }
      .count {
        width: 6.25rem;
        text-align: center;
      }
      .score-count {
        background: url("../../../../../asset//images//achievement/score_start.png") no-repeat
          center center/ contain;
        width: 5.3125rem;
        text-align: center;
        color: #fff;
        font-size: 1.25rem;
        padding-left: 0.875rem;
      }
    }
  }
}

.medal-list {
  position: relative;
  width: 20.625rem;
  height: 26.875rem;
  left: -1.25rem;
  .medal-background,
  .medal-content {
    width: 100%;
    position: absolute;
    left: 0;
    top: 0;
  }
  .medal-content {
    z-index: 2;
    padding: 1.25rem;
    .medal-level-title {
      color: #323232;
      font-family: "Alibaba PuHuiTi";
      font-size: 1rem;
      font-style: normal;
      font-weight: 700;
    }
    .medal-level-item {
      img {
        width: 100%;
      }
    }
    .medal-title {
      color: #323232;
      font-family: "Alibaba PuHuiTi";
      font-size: 1rem;
      font-style: normal;
      font-weight: 700;
      margin-bottom: 1.25rem;
    }
    .medal-items {
      display: flex;
      flex-wrap: wrap;
      gap: 1.25rem 0.625rem;
    }
    .medal-item {
      width: 5.625rem;
      text-align: center;
      .medal-icon {
        width: 3.75rem;
        height: 3.75rem;
      }
      .name {
        font-size: 0.9rem;
        font-weight: bold;
      }
    }
  }
  .medal-des {
    position: absolute;
    height: calc(100% - 1.125rem);
    bottom: 0.625rem;
    width: calc(100% - 1.0625rem);
    margin-top: 0.625rem;
    margin-left: 0.46875rem;
    padding: 0.625rem;
    background: rgba(0, 0, 0, 0.8);
    border-radius: 1rem;
    z-index: 3;
    color: #fff;
    &.level {
      .icon {
        padding-bottom: 0rem;
        margin-top: 1.25rem;
      }
    }
    .close-icon {
      position: relative;
      top: 0.3125rem;
      left: 0.3125rem;
      img {
        width: 1.875rem;
        height: 1.875rem;
      }
    }
    .icon {
      text-align: center;
      margin-top: 2.5rem;
      img {
        width: 5rem;
        height: 5rem;
      }
      padding-bottom: 1.25rem;
      border-bottom: 0.0625rem solid #ffffff67;
    }

    .description {
      margin-top: 1.25rem;
      text-align: center;
      white-space: pre-line;
      color: #fff;
      font-family: "Alibaba PuHuiTi";
      font-size: 1rem;
      font-style: normal;
      font-weight: 400;
      &.level {
        text-align: left;
        margin-top: 2.5rem;
      }
    }

    .level-key {
      font-size: 1.25rem;
      font-style: italic;
    }
    .level-des {
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      top: 1.0625rem;
      img {
        width: 2.125rem;
        height: 2.125rem;
        margin-right: 0.625rem;
        &.current{
          background: url("../../../../../asset/images/achievement/level_medals/lv_current.png") no-repeat center center/ contain;
        }
      }
    }
  }
}
