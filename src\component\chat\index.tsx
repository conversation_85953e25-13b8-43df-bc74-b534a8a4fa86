import React, {
  PropsWithChildren, useCallback, useImperativeHandle, forwardRef, useState,
} from 'react'
import { sleep } from 'cloud-office-util/help'
import {
  IChatProps
} from './interface'
import useChat from './hooks/useChat'
import FloatingWidget from './components/FloatingWidget'
import Chat<PERSON>indow from './components/ChatWindow'
import defaultUserAvatarPng from './images/userAvatar.png'
import defaultAvatarPng from './images/avatar.png'
import defaultChatInPng from './images/chatIn.png'

export interface IChatRef {
  openChat: () => void
  closeChat: () => void
  toggleChat: () => void
  sendMessage: (message: string, role?: 'user' | 'system') => Promise<void>
  regenerateMessage: (messageId: string) => Promise<void>
  clearMessages: () => void
  showNotification: (content: string, duration?: number) => void
}

// 定义组件类型，包含 ref
export type ChatComponent = React.ForwardRefExoticComponent<PropsWithChildren<IChatProps> & React.RefAttributes<IChatRef>>

const Chat = forwardRef<IChatRef, PropsWithChildren<IChatProps>>((props, ref) => {
  const {
    userAvatar = defaultUserAvatarPng,
    avatar = defaultAvatarPng,
    chatInAvatar = defaultChatInPng,
    title,
    width,
    height,
    enableScreenshot,
    enableLike,
    enableVoice,
    enableRegenerate,
    ttsConfig,
    className = '', // 添加 className 属性
    onScreenshot,
    onLike,
    onVoice,
    onTtsConfigChange,
    onClick,
    onClose,
  } = props

  const {
    visible,
    setVisible,
    position,
    setPosition,
    messages,
    isLoading,
    sendMessage,
    regenerateMessage,
    interruptConversation,
    clearMessages,
    initializeTTS,
    ttsHook,
  } = useChat(props)

  // 消息通知状态
  const [notification, setNotification] = useState('')

  // 显示消息通知
  const showNotification = useCallback((content: string, duration = 3000) => {
    setNotification(content)
    setTimeout(() => {
      setNotification('')
    }, duration)
  }, [])

  // 暴露给外部的方法
  useImperativeHandle(ref, () => ({
    openChat: () => setVisible(true),
    closeChat: () => setVisible(false),
    toggleChat: () => setVisible(!visible),
    sendMessage,
    regenerateMessage,
    clearMessages,
    showNotification,
  }), [visible, setVisible, sendMessage, regenerateMessage, clearMessages, showNotification])

  // 切换聊天窗口显隐
  const toggleChat = useCallback(() => {
    setVisible(!visible)
  }, [visible, setVisible])

  // 关闭聊天窗口
  const closeChat = useCallback(() => {
    setVisible(false)
    onClose?.()
    // eslint-disable-next-line @typescript-eslint/no-use-before-define
    handleInterruptSpeech()
  }, [setVisible])

  // 处理消息点赞
  const handleLikeMessage = useCallback((messageId: string) => {
    onLike?.(messageId)
  }, [onLike])

  // 处理消息截图
  const handleScreenshotMessage = useCallback(async (messageId: string) => {
    // 找到对应的消息
    const message = messages.find((m) => m.id === messageId)
    if (!message) {
      console.error('Message not found:', messageId)
      return
    }

    // 调用外部截图处理
    onScreenshot?.(messageId)
  }, [messages, onScreenshot])

  // 处理消息语音播放
  const handleVoiceMessage = async (messageId: string) => {
    if (onVoice) {
      onVoice(messageId)
    }
    const message = messages.find((m) => m.id === messageId)
    if (message !== undefined && message.content !== '') {
      try {
        ttsHook.stopTTS()
        // 直接调用 TTS 开始播放
        await initializeTTS()
        await sleep(1000) // 确保 TTS 初始化完成
        await ttsHook.sendText(message.content)
      } catch (error) {
        console.error('Speech failed:', error)
      }
    }
  }

  // 处理消息重新生成
  const handleRegenerateMessage = useCallback((messageId: string) => {
    // 直接调用regenerateMessage函数
    regenerateMessage(messageId).catch(console.error)
  }, [regenerateMessage])

  // 中断语音
  const handleInterruptSpeech = useCallback(() => {
    interruptConversation()
  }, [interruptConversation])

  return (
    <div className={className}>
      {/* 智能浮窗 */}
      <FloatingWidget
        visible={!visible}
        position={position}
        avatar={chatInAvatar}
        onPositionChange={setPosition}
        onToggleChat={toggleChat}
        onClick={onClick}
        notificationContent={notification}
      />

      {/* 对话窗口 */}
      <ChatWindow
        userAvatar={userAvatar}
        avatar={avatar}
        visible={visible}
        title={title}
        width={width}
        height={height}
        messages={messages?.filter((item) => item.role !== 'system')}
        isLoading={isLoading}
        onClose={closeChat}
        onSendMessage={sendMessage}
        onLikeMessage={handleLikeMessage}
        onScreenshotMessage={handleScreenshotMessage}
        onVoiceMessage={handleVoiceMessage}
        onRegenerateMessage={handleRegenerateMessage}
        onInterruptSpeech={handleInterruptSpeech}
        onTtsConfigChange={onTtsConfigChange}
        ttsConfig={ttsConfig}
        enableScreenshot={enableScreenshot}
        enableLike={enableLike}
        enableVoice={enableVoice}
        enableRegenerate={enableRegenerate}
      />
    </div>
  )
}) as ChatComponent

Chat.displayName = 'Chat'

export default Chat
