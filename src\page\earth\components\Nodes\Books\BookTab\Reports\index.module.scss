.reports {
  height: 100%;
  width: 95.4%;
  position: relative;
  left: 3.625rem;
  background: url("../../../../../../../asset/images/books/reports/report_bg.png") no-repeat center
    center / cover;
  .checklist {
    position: absolute;
    top: 2.5rem;
    right: 2.5rem;
    width: 7.125rem;
  }
  .reports-title {
    font-size: 1.5rem;
    font-style: normal;
    font-weight: 700;
    color: #655c5c;
    margin-right: 2.75rem;
    position: relative;
    text-shadow: 0 0.1875rem 0 #fff;
    top: 2.5rem;
    left: 50%;
    display: inline-block;
    width: 6rem;
    transform: translate(-3rem);

    .reports-key {
      position: absolute;
      left: 0.625rem;
      top: 1.875rem;
      width: 4.375rem;
    }
  }
  .content {
    width: 100%;
    height: calc(100% - 6.25rem);
    position: relative;
    margin-top: 3.0625rem;

    .item {
      position: absolute;
      cursor: pointer;
      color: #c7bba5;
      font-size: 1rem;
      width: 11.6875rem;
      height: 2.25rem;
      line-height: 2.0625rem;
      &.active {
        color: #8b754d;
      }
      span {
        position: relative;
        z-index: 10;
      }
      .check-icon {
        left: 0;
        position: absolute;
        width: 100%;
        height: 100%;
        z-index: 1;
      }
      &.question {
        right: 35.625rem;
        top: 3.375rem;
        text-align: right;
        padding-right: 0.625rem;
      }
      &.clue {
        left: 35.625rem;
        top: 8.6875rem;
        padding-left: 0.625rem;
      }
      &.experiment {
        right: 35.625rem;
        top: 15rem;
        text-align: right;
        padding-right: 0.625rem;
      }
      &.presume {
        left: 35.625rem;
        top: 19.5rem;
        padding-left: 0.625rem;
      }
      &.conclusion {
        right: 35.625rem;
        top: 27.1875rem;
        text-align: right;
        padding-right: 0.625rem;
      }
    }
    .detail {
      height: 100%;
      width: 15rem;
      position: absolute;
      background: url("../../../../../../../asset/images/books/reports/detail_bg.png") no-repeat
        center center;
      background-size: 100% 100%;
      overflow-y: auto;
      padding: 0.625rem;
      right: 624.9375rem;
      &.left {
        left: 1.6875rem;
      }
      &.right {
        right: 1.6875rem;
      }
      .detail-item {
        margin-bottom: 0.625rem;
        .item-label {
          color: #664b48;
          font-size: 1rem;
          font-style: normal;
          font-weight: 500;
        }
        .item-value {
          margin-top: 1.25rem;
          border: 0.625rem solid transparent;
          border-image: url("../../../../../../../asset/images/books/reports/item_bg.png") 20 fill;
          position: relative;
          min-height: 5rem;
          font-size: 1rem;
          color: #847674;
          &::before {
            content: "";
            position: absolute;
            top: -1.25rem;
            left: 0;
            width: 0.875rem;
            height: 0.875rem;
            background: url("../../../../../../../asset/images/books/reports/item_bg_arrow.png")
              no-repeat left top;
            background-size: contain;
            z-index: 1;
          }
          &.conclusion {
            &::before {
              background: unset;
            }
          }
        }
        img {
          width: 100%;
        }
      }
    }
  }
}

.pop_overlay {
  :global {
    .fish-popover-arrow {
      display: none;
    }
    .fish-popover-inner {
      box-shadow: unset;
      background: transparent;
    }
    .fish-popover-inner-content {
      padding: 0rem;
    }
  }
  .pop_bg {
    width: 18.125rem;
    height: 33.125rem;
    overflow-y: auto;
    background: url("../../../../../../../asset/images/books/reports/checklist_bg.png") no-repeat
      center center;
    background-size: 100% 100%;
    padding: 1.25rem;
    .check-item {
      color: #664b48;
      font-size: 1rem;
      font-style: normal;
      font-weight: 500;
      padding: 0.625rem;
      &:not(:last-child) {
        border-bottom: 0.0625rem solid #eae5dc;
      }
      img {
        width: 1.125rem;
        height: 1.125rem;
        margin-right: 0.625rem;
      }
    }
  }
}
