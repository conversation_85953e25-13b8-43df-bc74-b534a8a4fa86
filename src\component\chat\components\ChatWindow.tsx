import React, {
  FC, useState, useRef, useCallback, useEffect
} from 'react'
import { IChatWindowProps } from '../interface'
import MessageItem from './MessageItem'
import CustomSwitch from './CustomSwitch'
import styles from './ChatWindow.module.scss'
import titleIcon from '../images/title_icon.png'

const ChatWindow: FC<IChatWindowProps> = ({
  userAvatar,
  avatar,
  visible,
  title = 'AI助手',
  width = 400,
  height = 600,
  messages,
  isLoading = false,
  onClose,
  onSendMessage,
  onLikeMessage,
  onScreenshotMessage,
  onVoiceMessage,
  onRegenerateMessage,
  onInterruptSpeech,
  ttsConfig,
  onTtsConfigChange,
  enableScreenshot = true,
  enableLike = true,
  enableVoice = true,
  enableRegenerate = true
}) => {
  const [inputValue, setInputValue] = useState('')
  const [isComposing, setIsComposing] = useState(false)
  const inputRef = useRef<any>(null)
  const messageListRef = useRef<HTMLDivElement>(null)

  // 滚动到底部
  const scrollToBottom = useCallback(() => {
    if (messageListRef.current) {
      messageListRef.current.scrollTop = messageListRef.current.scrollHeight
    }
  }, [])

  // 当消息变化时自动滚动到底部
  useEffect(() => {
    if (messages.length > 0) {
      // 使用 setTimeout 确保 DOM 更新完成后再滚动
      setTimeout(() => {
        scrollToBottom()
      }, 100)
    }
  }, [messages, scrollToBottom])

  // 发送消息
  const handleSend = useCallback(() => {
    const content = inputValue.trim()
    if (content === '' || isLoading) return

    onSendMessage(content)
    setInputValue('')
    // 发送消息后滚动到底部
    setTimeout(() => {
      scrollToBottom()
    }, 100)
  }, [inputValue, isLoading, onSendMessage, scrollToBottom])

  // 中断语音和对话
  const handleInterrupt = useCallback(() => {
    onInterruptSpeech?.()
  }, [onInterruptSpeech])

  // 处理键盘事件
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey && !isComposing) {
      e.preventDefault()
      if (isLoading) {
        handleInterrupt()
      } else {
        handleSend()
      }
    }
  }, [handleSend, isComposing])

  // 处理自动语音播报开关变化
  const handleSpeechToggle = useCallback((checked: boolean) => {
    if (ttsConfig && onTtsConfigChange) {
      onTtsConfigChange({
        ...ttsConfig,
        enabled: checked
      })
    }
  }, [ttsConfig, onTtsConfigChange])

  if (!visible) return null

  return (
    <div className={styles.chatWindow} style={{ width, height }}>
      <div
        className={styles.chatMessages}
      >
        {/* 头部 */}
        <div className={styles.header}>
          <div className={styles.title}>
            <img src={titleIcon} />
            {title}
          </div>
          <div className={styles.actions}>
            <div className={styles.speechSwitch}>
              <span>语音播报</span>
              <CustomSwitch
                checkedChildren="开"
                unCheckedChildren="关"
                checked={ttsConfig?.enabled || false}
                onChange={handleSpeechToggle}
              />
            </div>
            <button
              className={styles.closeBtn}
              onClick={onClose}
              title="关闭"
            >
              ✕
            </button>
          </div>
        </div>

        {/* 消息列表 */}
        <div className={styles.messageList} ref={messageListRef}>
          {messages.length === 0
            ? (
              <div className={styles.emptyState}>
                <div className={styles.emptyIcon}>💬</div>
                <div className={styles.emptyText}>开始您的AI对话吧</div>
              </div>
            )
            : (
              <div className={styles.messages}>
                {messages.map((message, index) => (
                  <MessageItem
                    key={`${message.id || index}-${message.timestamp || index}`}
                    message={message}
                    userAvatar={userAvatar}
                    avatar={avatar}
                    onLike={onLikeMessage}
                    onScreenshot={onScreenshotMessage}
                    onVoice={onVoiceMessage}
                    onRegenerate={onRegenerateMessage}
                    enableScreenshot={enableScreenshot}
                    enableLike={enableLike}
                    enableVoice={enableVoice}
                    enableRegenerate={enableRegenerate}
                    hideOperation={message.status === 'error' || index === 0}
                  />
                ))}
              </div>
            )}
        </div>
      </div>

      {/* 输入区域 */}
      <div className={styles.inputContainer}>
        <input
          ref={inputRef}
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          onKeyDown={handleKeyDown}
          onCompositionStart={() => setIsComposing(true)}
          onCompositionEnd={() => setIsComposing(false)}
          placeholder="输入你的问题...(50字以内)"
          className={styles.textInput}
          maxLength={50}
        />
      </div>
    </div>
  )
}

export default ChatWindow
