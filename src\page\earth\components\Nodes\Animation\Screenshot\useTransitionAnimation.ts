import { useCallback, useState } from 'react'

interface AnimationConfig {
  x: number
  y: number
  scale?: number
  width?: number
  height?: number
  opacity: number
  duration: number // 动画持续时间，单位秒
  easing?: string // 动画缓动函数，默认 'ease-out'
}

interface CurrentState {
  x: number
  y: number
  scale: number
  width: number
  height: number
  opacity: number
}

const useTransitionAnimation = () => {
  const [isAnimating, setIsAnimating] = useState(false)

  // 获取元素当前状态
  const getCurrentState = useCallback((element): CurrentState => {
    if (!element) {
      return {
        x: 0, y: 0, width: 0, scale: 1, height: 0, opacity: 1
      }
    }

    const rect = element.getBoundingClientRect()
    const computedStyle = window.getComputedStyle(element)
    return {
      x: rect.left,
      y: rect.top,
      scale: parseFloat(computedStyle.scale) || 1,
      width: rect.width,
      height: rect.height,
      opacity: parseFloat(computedStyle.opacity) || 1
    }
  }, [])

  // 执行动画
  const animateTo = useCallback(async (element, config: AnimationConfig): Promise<void> => new Promise((resolve) => {
    if (!element || isAnimating) {
      resolve()
      return
    }

    setIsAnimating(true)
    const currentState = getCurrentState(element)

    // 设置初始状态
    element.style.position = 'fixed'
    element.style.left = `${currentState.x}px`
    element.style.top = `${currentState.y}px`
    element.style.scale = `${currentState.scale}`
    element.style.width = `${currentState.width}px`
    element.style.height = `${currentState.height}px`
    element.style.opacity = `${currentState.opacity}`
    element.style.transform = 'none' // 清除原有的transform
    element.style.transformOrigin = 'top left' // 设置缩放原点为左上角
    element.style.transition = 'none' // 确保初始状态立即生效

    // 强制重绘
    // eslint-disable-next-line @typescript-eslint/no-unused-expressions
    element.offsetHeight

    // 设置过渡动画
    const easing = config.easing || 'ease-out'
    element.style.transition = `all ${config.duration}s ${easing}`

    // 设置目标状态
    element.style.left = `${config.x || currentState.x}px`
    element.style.top = `${config.y || currentState.y}px`
    element.style.scale = config.scale || currentState.scale
    element.style.width = `${config.width || currentState.width}px`
    element.style.height = `${config.height || currentState.height}px`
    element.style.opacity = `${config.opacity || currentState.opacity}`

    // 动画结束后的处理
    const handleTransitionEnd = () => {
      element.removeEventListener('transitionend', handleTransitionEnd)
      setIsAnimating(false)
      resolve()
    }

    element.addEventListener('transitionend', handleTransitionEnd)

    // 备用方案：如果 transitionend 事件没有触发，使用 setTimeout
    setTimeout(() => {
      if (isAnimating) {
        element.removeEventListener('transitionend', handleTransitionEnd)
        setIsAnimating(false)
        resolve()
      }
    }, config.duration * 1000 + 100) // 多等待100ms确保动画完成
  }), [isAnimating, getCurrentState])

  // 重置元素样式
  const resetElement = useCallback((element) => {
    if (element) {
      element.style.position = ''
      element.style.left = ''
      element.style.top = ''
      element.style.scale = ''
      element.style.width = ''
      element.style.height = ''
      element.style.opacity = ''
      element.style.transform = ''
      element.style.transformOrigin = ''
      element.style.transition = ''
    }
    setIsAnimating(false)
  }, [])

  return {
    isAnimating,
    animateTo,
    resetElement,
    getCurrentState,
  }
}

export default useTransitionAnimation
export type { AnimationConfig, CurrentState }
