.nodes {
  height: 100%;
  width: 100%;
  position: relative;
  .header {
    position: fixed;
    z-index: 2;
    width: 100%;
    user-select: none;
    &.guide_header {
      z-index: 1001;
      .node-title{
        z-index: 5;
        position: relative;
      }
    }
    .over {
      background: rgba(0, 0, 0, 0.6);
      position: absolute;
      width: 100%;
      height: 3.75rem;
      top: 0;
      left: 0;
      z-index: 3;
    }
    .node-title {
      cursor: pointer;
      width: 30rem;
      height: 2.75rem;
      background: url("../../../../asset/images/header_center/title_bg.png") no-repeat center center /
        cover;
      text-align: center;
      margin: 0 auto;
      line-height: 2.75rem;
      color: #fff;
      font-weight: bold;
      .node-title-text {
        overflow: hidden;
        color: #fff;
        text-align: center;
        text-overflow: ellipsis;
        text-shadow: 0rem 0.25rem 0.25rem rgba(79, 38, 99, 0.25);
        font-family: "Alibaba PuHuiTi";
        font-style: normal;
        font-weight: 700;
        line-height: 2.75rem;
        letter-spacing: 0.125rem;
      }
      .node-title-icon {
        background: url("../../../../asset/images/header_center/down_default.png") no-repeat center
          center / cover;
        display: inline-block;
        width: 1.25rem;
        height: 1.25rem;
        position: relative;
        top: 0.25rem;
        left: 0.1875rem;
        &.expand {
          background: url("../../../../asset/images/header_center/up_default.png") no-repeat center
            center / cover;
        }
      }
      &:hover {
        .node-title-icon {
          background: url("../../../../asset/images/header_center/down_hover.png") no-repeat center
            center / cover;
          &.expand {
            background: url("../../../../asset/images/header_center/up_hover.png") no-repeat center
              center / cover;
          }
        }
      }
    }
    .node-list {
      margin-top: 1.25rem;
      padding: 1.25rem;
      width: 35rem;
      height: 35.625rem;
      margin: 0rem auto;
      background: url("../../../../asset/images/header_center//list_bg.png") no-repeat center center /
        contain;
      position: relative;
      top: 1.25rem;
      display: none;
      &.expand {
        display: flex;
        flex-direction: column;
      }
      .node-list-header {
        color: #fff;
        font-style: normal;
        font-weight: 700;
        text-shadow: -0.0625rem -0.0625rem 0 #000, 0.0625rem -0.0625rem 0 #000, -0.0625rem 0.0625rem 0 #000, 0.0625rem 0.0625rem 0 #000;
        position: relative;
        top: -0.5rem;
        left: 2.5rem;
      }
      .node-list-content {
        margin-top: 0.625rem;
        width: 100%;
        height: 0rem;
        overflow: auto;
        flex: 1;
        border-radius: 0.875rem;

        .node-list-item {
          padding-left: 1.25rem;
          height: 3.5625rem;
          line-height: 3.5625rem;
          background: url("../../../../asset/images/header_center/item.png") no-repeat center center /
            cover;
          &.active {
            background: url("../../../../asset/images/header_center/item_active.png") no-repeat
              center center/ cover;
          }
        }
      }
    }
  }
  .right {
    position: absolute;
    z-index: 2;
    right: 7.5rem;
    top: 0.875rem;
    display: flex;
    align-items: center;
    button {
      margin-left: 1.25rem;
    }
  }

  .next {
    background: url("../../../../asset/images/header_right/next_pre.png") no-repeat center center /
      cover;
    position: fixed;
    top: 1.125rem;
    right: 1.25rem;
    z-index: 2;
    width: 6.5rem;
    height: 2.125rem;
    line-height: 1.875rem;
    text-align: center;
    user-select: none;
    color: #323232;
    font-family: "Alibaba PuHuiTi";
    font-style: normal;
    font-weight: 700;
    &.top {
      z-index: 1001;
    }
    &:active {
      background: url("../../../../asset/images/header_right/next_click.png") no-repeat center
        center / cover;
    }
    &:touch-active {
      background: url("../../../../asset/images/header_right/next_click.png") no-repeat center
        center / cover;
    }
  }

  .content {
    width: 100%;
    height: 100%;
    z-index: 1;
    flex: 1;
  }
}
