import { createSlice, PayloadAction } from '@reduxjs/toolkit'
import { v4 as uuidv4 } from 'uuid'
import { IEarthConfigInfo, IEarthData, UPGRADE_KEY } from './interface'
import { MedalKey } from './components/Nodes/Achievement/constant'

export const DEFAULT_DATA: IEarthData = {
  currentGroupId: '',
  currentNodeId: '',
  prevNode: undefined,
  manualBook: true,
  questionUnLock: false,
  iterateUnLock: false,
  simulateUnLock: false,
  guided: {},
  config: {
    aiLevel: 'middle',
    achievement: true,
    aiMode: true
  },
  books: {
    hypothesis: {
      clue: [],
      presumes: [{
        id: uuidv4(),
        out: {
          value: '',
          relation_clue: ['', '', '']
        },
        middle: {
          value: '',
          relation_clue: ['', '', '']
        },
        in: {
          value: '',
          relation_clue: ['', '', '']
        },
      }],
      question_presumes: [{
        id: uuidv4(),
        question: '',
        presume: '',
        relation_clue: ['', '', ''] // 渲染3个表单
      }]
    },
    experiment: [],
    conclusion: {
      conclusion_value: ''
    },
    reports: {}
  },
  achievement: {
    medals: Object.keys(MedalKey).map((key) => ({
      time: 0,
      key: MedalKey[key as keyof typeof MedalKey],
      unlocked: false,
      upgrade: key === MedalKey.earth_researcher ? Object.keys(UPGRADE_KEY).reduce((acc, cur) => {
        acc[cur] = false
        return acc
      }, {} as unknown as Record<UPGRADE_KEY, boolean>) : undefined
    }))
  },
  ai: {
    messages: [],
    loading: false,
    error: '',
    activeCount: 0,
    count: 0
  },
  simulate: {
    errorCount: 0
  }
}

export enum STEP {
  HOME = 'home', // 首页
  SAVES = 'saves', // 存档页
  NODES = 'nodes' // 节点信息
}
interface IEarthState {
  data: IEarthData // 存db
  nodeInfo: IEarthConfigInfo
  config: { // 不存db
    saves: any[]// 存档列表
    currentSaveId?: string // 当前存档ID
    step: STEP
  }
}

const initialEarthState: IEarthState = {
  data: DEFAULT_DATA,
  nodeInfo: {} as unknown as IEarthConfigInfo,
  config: {
    saves: [],
    currentSaveId: '',
    step: STEP.HOME
  }
}

const earthSlice = createSlice({
  name: 'earth',
  initialState: initialEarthState,
  reducers: {
    updateData: (state, action: PayloadAction<any>) => {
      for (const key of Object.keys(action.payload)) {
        state.data[key] = action.payload[key]
      }
    },
    updateConfig: (state, action: PayloadAction<any>) => {
      for (const key of Object.keys(action.payload)) {
        state.config[key] = action.payload[key]
      }
    },
    setNodeInfo: (state, action: PayloadAction<any>) => {
      state.nodeInfo = action.payload
    }
  }
})

export const {
  updateData,
  updateConfig,
  setNodeInfo
} = earthSlice.actions

export default earthSlice.reducer
