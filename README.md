## 如何使用

1. npm install 安装依赖

## 命令

```bash

# 默认开启服务器，地址为 ：http://localhost:8080/

# 安装依赖
npm install

# 能在ie9+运行热更新，页面会自动刷新
npm run start:{env}

```

## 目录说明

```
./
├── src/                       // 项目源代码
├── ├── @types                 // 全局类型定义
├── ├── config                 // one、ndr、请求等配置
├── ├── page                   // 模块的开发目录
├── ├── ├── courseware         // 一个教学活动颗粒的生产
├── ├── route                  // 路由
├── ├── component              // 组件
├── ├── asset                  // 静态文件，如公共的图片、资源等（目录需保留，里面的内容可删除）
├── ├── store                  // 生成redux的store，并关联所有的reducer
├── ├── utils                  // 工具箱
├── ├── index.ts               // 站点的入口
```

## 项目约定


## 相关地址

1. 共享平台:
   https://d.101.com/#/home?appId=6865217124e3d757361bf61c

2. gitlab:
   https://gitlab.ndaeweb.com/app-web/aic-earth-research


