import * as React from 'react'
import ReactDOM from 'react-dom'
import { useLoginUser } from 'cloud-office-util/help'
import type { ICallbackParameter } from 'cloud-office-util/help/use-login-user'
import { enableMapSet } from 'immer'
import './config/request'
import { HashRouter as Router, useRoutes } from 'react-router-dom'
import { Provider } from 'react-redux'
import routerConfig from './route'
import I18N from './i18n'
import bootstrap from './config/bootstrap'
import './asset/style/index.less'

import { update } from './userSlice'
import store, { useAppDispatch, useAppSelector } from './store'

enableMapSet()

const RouteElement = () => useRoutes(routerConfig)

const App = (() => {
  const accountId = useAppSelector((state) => state.user.accountId)
  const dispatch = useAppDispatch()

  const callback = (params?: ICallbackParameter) => {
    const init = async () => {
      dispatch(update(params))
    }

    // NDR 已经初始化成功
    if (window.NDR) {
      init()
    } else {
      const handler = () => {
        init()
        window.removeEventListener('DOMContentLoaded', handler)
      }
      // 等待NDR脚本先执行成功
      window.addEventListener('DOMContentLoaded', handler)
    }
  }

  useLoginUser({
    callback,
    guest: true
  })

  if (accountId === undefined) {
    return null
  }

  return (
    <I18N bootstrap={bootstrap}>
      <Router>
        <RouteElement />
      </Router>
    </I18N>
  )
})

ReactDOM.render(
  (
    <Provider store={store}>
      <App />
    </Provider>
  ),
  document.querySelector('#root')
)
