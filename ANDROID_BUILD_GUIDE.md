# 📱 Android APK 编译构建指南

## 📋 目录
- [技术栈概述](#技术栈概述)
- [构建命令详解](#构建命令详解)
- [完整编译流程详解](#完整编译流程详解)
- [签名配置](#签名配置)
- [构建产物说明](#构建产物说明)
- [开发工作流](#开发工作流)
- [故障排除](#故障排除)

---

## 🔧 技术栈概述

### 核心架构
```
React Web应用 → Capacitor → Android原生应用
```

### 构建工具链
- **前端构建**: GemMine Scripts (基于Webpack)
- **移动端桥接**: Capacitor 3.4.0
- **Android构建**: Gradle + Android SDK
- **签名管理**: 自定义Keystore配置

---

## 📦 构建命令详解

### Package.json 中的关键命令

#### 🛠️ 新的参数化构建命令 (推荐)

```bash
# 🎯 你要的命令示例:
npm run android:release --env=product --remote=false         # 离线生产环境Release APK

# 🌟 新的参数化构建方式 - 支持环境和远程模式参数

# 1、本地模式构建 (使用dist/下的静态文件)
npm run android --env=test                    # Debug APK - 测试环境
npm run android --env=preproduction           # Debug APK - 预生产环境  
npm run android --env=product                 # Debug APK - 生产环境
npm run android --env=sdpsg                   # Debug APK - SDPSG环境

npm run android:release --env=test            # Release APK - 测试环境
npm run android:release --env=preproduction   # Release APK - 预生产环境
npm run android:release --env=product         # Release APK - 生产环境
npm run android:release --env=sdpsg           # Release APK - SDPSG环境

# 2、在线模式构建 (直接访问远程URL，不加载本地文件)
npm run android --env=test --remote=true                    # Debug APK - 在线测试环境
npm run android --env=preproduction --remote=true           # Debug APK - 在线预生产环境
npm run android --env=product --remote=true                 # Debug APK - 在线生产环境
npm run android --env=sdpsg --remote=true                   # Debug APK - 在线SDPSG环境

npm run android:release --env=test --remote=true            # Release APK - 在线测试环境
npm run android:release --env=preproduction --remote=true   # Release APK - 在线预生产环境
npm run android:release --env=product --remote=true         # Release APK - 在线生产环境
npm run android:release --env=sdpsg --remote=true           # Release APK - 在线SDPSG环境
```

#### 🔄 核心构建流程
```bash
# 1. 生产环境Web构建
npm run cap:build
├── cross-env SDP_ENV=product npm run build  # React应用构建到dist/
└── npx cap sync                              # 同步到Android项目

# 2. Android原生编译
cd android && .\gradlew assembleDebug/Release  # Gradle构建APK
```

---

## 🎯 完整编译流程详解

### 第一步：Web应用构建
```bash
cross-env SDP_ENV=product npm run build
```
**执行内容**:
1. **环境变量设置**: `SDP_ENV=product` 配置生产环境
2. **React构建**: `gms build` 编译TypeScript/React代码
3. **资源处理**: 
   - CSS/SCSS编译和优化
   - 图片资源压缩
   - JavaScript代码分割和压缩
4. **输出目录**: 所有构建产物输出到 `dist/` 目录

### 第二步：Capacitor同步
```bash
npx cap sync
```
**执行内容**:
1. **复制Web资源**: `dist/` → `android/app/src/main/assets/public/`
2. **插件配置同步**: 
   - 更新Capacitor插件配置
   - 同步原生权限设置
3. **项目结构更新**: 
   - 更新Android项目依赖
   - 同步配置文件变更

### 第三步：Android编译
```bash
# Debug版本
.\gradlew assembleDebug

# Release版本  
.\gradlew assembleRelease
```
**执行内容**:
1. **依赖解析**: 下载和解析Android依赖库
2. **Java/Kotlin编译**: 编译原生Android代码
3. **资源打包**: 合并和优化Android资源文件
4. **APK生成**: 
   - Debug: 使用debug.keystore签名
   - Release: 使用aic-earth-research.keystore签名
5. **代码优化**: Release版本进行ProGuard代码混淆

---

## 🔐 签名配置

### Keystore配置文件 (`android/keystore.properties`)
```properties
# Release配置
RELEASE_STORE_FILE=aic-earth-research.keystore
RELEASE_STORE_PASSWORD=aicearth123
RELEASE_KEY_ALIAS=aic-earth-research
RELEASE_KEY_PASSWORD=aicearth123

# Debug配置  
DEBUG_STORE_FILE=debug.keystore
DEBUG_STORE_PASSWORD=android
DEBUG_KEY_ALIAS=androiddebugkey
DEBUG_KEY_PASSWORD=android

# 构建配置
BUILD_TOOLS_VERSION=30.0.3
COMPILE_SDK_VERSION=30
TARGET_SDK_VERSION=30
MIN_SDK_VERSION=22
```

### 签名证书详情

#### Release Keystore (生产环境)
- **文件**: `android/app/aic-earth-research.keystore`
- **格式**: PKCS12
- **证书信息**: CN=AIC Earth Research, OU=Development, O=AIC, L=Beijing, ST=Beijing, C=CN
- **有效期**: 2025-7-16 至 2052-12-1 (27年)
- **签名算法**: SHA256withRSA, 2048位密钥

#### Debug Keystore (开发环境)
- **文件**: `android/app/debug.keystore`
- **格式**: PKCS12
- **证书信息**: CN=Android Debug, O=Android, C=US
- **有效期**: 2025-7-16 至 2052-12-1 (27年)

---

## 📦 构建产物说明

### APK输出位置
```
android/app/build/outputs/apk/
├── debug/
│   └── app-debug.apk          # Debug版本 (~3.19MB)
└── release/
    └── app-release.apk        # Release版本 (~2.63MB)
```

### 构建变体对比

| 构建类型 | 文件大小 | 签名 | 调试功能 | 代码混淆 | 使用场景 |
|---------|---------|------|----------|----------|----------|
| Debug   | ~3.19MB | debug.keystore | ✅ 启用 | ❌ 关闭 | 开发调试 |
| Release | ~2.63MB | aic-earth-research.keystore | ❌ 关闭 | ✅ 启用 | 生产发布 |

### 应用信息
- **应用ID**: `com.aic.earth.research`
- **应用名称**: `地球侦探笔记`
- **最小SDK**: 22 (Android 5.1+)
- **目标SDK**: 30 (Android 11)

---

## 🚀 开发工作流

### 日常开发流程
```bash
# 1. 修改React代码
# 2. 构建并测试
npm run android:debug    # 实时调试模式

# 3. 构建APK进行测试
npm run android:build    # 生成Debug APK

# 4. 安装到设备测试
adb install android/app/build/outputs/apk/debug/app-debug.apk
```

### 生产发布流程
```bash
# 1. 最终代码提交
# 2. 构建Release APK
npm run android:build:release

# 3. 验证APK签名
jarsigner -verify -verbose -certs android/app/build/outputs/apk/release/app-release.apk

# 4. 发布到应用商店或分发平台
```

## 🛠️ 故障排除

### 常见问题及解决方案

#### 1. Gradle构建失败
```bash
# 清理项目
npm run android:clean

# 重新同步
npm run cap:sync

# 重新构建
npm run android:build
```

#### 2. 签名验证失败
```bash
# 检查keystore文件是否存在
ls android/app/*.keystore

# 验证签名配置
cat android/keystore.properties

# 重新生成keystore（如果需要）
keytool -genkey -v -keystore android/app/debug.keystore -storepass android -alias androiddebugkey -keypass android -keyalg RSA -keysize 2048 -validity 10000 -storetype PKCS12
```

#### 3. Web资源未更新
```bash
# 强制重新构建Web应用
npm run build

# 强制同步到Android
npx cap sync --force

# 清理Android缓存后重新构建
npm run android:clean && npm run android:build
```

### 环境要求检查
```bash
# 检查Node.js版本 (需要14.21.3)
node --version

# 检查Java版本 (需要Java 11+)
java --version

# 检查Android SDK
echo $ANDROID_HOME

# 检查Gradle版本
cd android && .\gradlew --version
```