import { useRef, useCallback } from 'react'
import html2canvas from 'html2canvas'

// 等待元素内所有图片加载完成
function waitForImagesToLoad(element: HTMLElement): Promise<void> {
  return new Promise((resolve) => {
    const images = element?.querySelectorAll('img') || []
    if (images.length === 0) {
      resolve()
      return
    }

    let loadedCount = 0
    const totalImages = images.length

    const checkAllLoaded = () => {
      loadedCount += 1
      if (loadedCount === totalImages) {
        resolve()
      }
    }

    images.forEach((img) => {
      if (img.complete && img.naturalWidth > 0) {
        // 图片已经加载完成
        checkAllLoaded()
      } else {
        // 监听图片加载完成事件
        img.onload = checkAllLoaded
        img.onerror = checkAllLoaded // 即使加载失败也要继续
      }
    })
  })
}

const useScreenshot = () => {
  const screenshotCardRef = useRef<HTMLDivElement>(null)

  const captureScreenshot = useCallback(async (
    onComplete?: (result: string) => void
  ) => {
    try {
      // 等待ScreenshotCard渲染
      setTimeout(async () => {
        if (screenshotCardRef.current) {
          try {
            const element = screenshotCardRef.current

            // 等待所有图片加载完成-外部等待 避免黑屏背景框
            // await waitForImagesToLoad(element)
            // 使用更简单的配置
            const canvas = await html2canvas(element, {
              useCORS: true, // 允许跨域图片
              allowTaint: true, // 允许污染
              scale: window.devicePixelRatio // 使用设备像素比
            })

            const result = canvas.toDataURL('image/png', 1.0)
            onComplete?.(result)
          } catch (error) {
            console.error('Screenshot failed:', error)
            if (error instanceof Error) {
              console.error('Error stack:', error.stack)
            }
          }
        } else {
          console.error('截图卡片元素未找到')
        }
      }, 100)
    } catch (error) {
      console.error('Screenshot process failed:', error)
    }
  }, [])

  return {
    screenshotCardRef,
    captureScreenshot,
    waitForImagesToLoad
  }
}

export default useScreenshot
