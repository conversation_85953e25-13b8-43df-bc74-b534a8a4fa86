.messageItem {
  margin-bottom: 1rem; // 统一消息间距
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 0 1rem;
  // 移除固定高度，让内容自适应

  &:last-child {
    margin-bottom: 0.5rem; // 最后一条消息的间隔稍小
  }

  &.userMessage {
    flex-direction: row-reverse;

    .messageContent {
      color: #2d2c40cc;
      max-width: 75%;
      border-radius: 1.25rem;
      border: 0.25rem solid rgba(255, 255, 255, 0.7);
      background: linear-gradient(180deg, #b4ffb9 -37.21%, #aff4ab 100%);
      box-shadow: 0 -0.0625rem 0.25rem 0 #b3fff7 inset;
      // 用户消息的滚动条样式（浅色）
      .messageText::-webkit-scrollbar-thumb {
        background: rgba(255, 255, 255, 0.3);

        &:hover {
          background: rgba(255, 255, 255, 0.5);
        }
      }
      .arrowIcon {
        position: absolute;
        top: 0;
        right: -1.125rem;
        z-index: 1;
        width: 1.25rem;
      }
      .bgIcon {
        position: absolute;
        bottom: 0;
        left: 0;
        max-width: 4.375rem;
        width: 100%;
      }
    }

    .messageAvatar {
      margin-left: 0.5rem;
    }
  }

  &.assistantMessage {
    flex-direction: row;
    .messageName {
      padding-left: 0.25rem;
    }
    .messageContent {
      color: #2d2c40cc;
      max-width: 85%;
      border-radius: 1.25rem;
      border: 0.25rem solid rgba(255, 255, 255, 0.7);
      background: linear-gradient(
          102deg,
          rgba(255, 255, 255, 0) 41.11%,
          rgba(150, 98, 255, 0.2) 113.05%
        ),
        linear-gradient(180deg, #b8eaff 0%, #cee9ff 100%);
      box-shadow: 0 -0.0625rem 0.25rem 0 #b3fff7 inset;
      .status {
        color: rgba(45, 44, 64, 0.4);
        margin-bottom: 0.625rem;
        img {
          width: 1.25rem;
          height: 0.75rem;
          margin-left: 0.5rem;
        }
      }
      .arrowIcon {
        position: absolute;
        top: 0;
        left: -1.125rem;
        z-index: 1;
        width: 1.25rem;
      }
      .bgIcon {
        position: absolute;
        bottom: 0;
        right: 0;
        max-width: 4.375rem;
        width: 100%;
      }
    }

    .messageAvatar {
      margin-right: 0.5rem;
    }
  }
}

.messageAvatar {
  width: 3.4375rem;
  height: 3.4375rem;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.1);

  .avatarImg {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .defaultAvatar {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    background: linear-gradient(135deg, #f0f0f0, #e0e0e0);
    color: #666;
    font-weight: 500;
  }
}
.messageContentBox {
  width: 100%;
}

.messageContent {
  padding: 0.875rem 1.125rem 0.875rem 1.125rem;
  position: relative;
  display: inline-block;
  transition: all 0.2s ease;
  font-size: 1rem;
  .messageName {
    font-size: 0.75rem;
    color: #666;
    margin-bottom: 0.25rem;
    font-weight: 500;
  }

  .messageText {
    line-height: 1.6;
    word-wrap: break-word;
    word-break: break-word;
    white-space: pre-wrap; // 保持换行和空格
    margin: 0;
    min-height: 1.6em; // 至少一行的高度
    max-height: 4.8em; // 最多显示3行 (1.6em * 3)
    overflow-y: auto; // 垂直滚动

    // 处理长文本
    overflow-wrap: break-word;
    hyphens: auto;

    // 确保内容对齐
    display: block;

    // 自定义滚动条样式
    &::-webkit-scrollbar {
      width: 0.25rem;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(0, 0, 0, 0.2);
      border-radius: 0.125rem;

      &:hover {
        background: rgba(0, 0, 0, 0.3);
      }
    }
  }
}

.messageActions {
  margin-top: 0.625rem;
  border-top: 0.0625rem solid rgba(32, 65, 112, 0.2);
  padding-top: 0.25rem;
  padding-right: 1.25rem;
  display: flex;
  align-items: center;
  gap: 0.375rem;
  flex-shrink: 0;

  .actionBtn {
    border: none;
    font-size: 1.125rem;
    cursor: pointer;
    padding: 0.375rem 0.625rem;
    padding-bottom: 0rem;
    border-radius: 0.75rem;
    transition: all 0.2s ease;
    opacity: 0.7;
    color: inherit;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    height: 1.875rem;
    &:first-child {
      padding-left: 0rem;
    }

    &:hover {
      opacity: 1;
      transform: translateY(-0.0625rem);
    }

    &:active {
      transform: translateY(0) scale(0.95);
    }

    &.reset {
      position: absolute;
      right: 0rem;
    }
    &.loading {
      opacity: 0.5;
      cursor: not-allowed;
      animation: spin 1s linear infinite;
    }

    &:disabled {
      cursor: not-allowed;
      opacity: 0.3;
      transform: none;
    }
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
