import React, {
  FC, useEffect,
  useMemo,
  useState,
  useRef
} from 'react'
import classNames from 'classnames/bind'
import EventBus from '@/component/EventBus'
import { Events } from '@/component/EventBus/constant'
import fingerLeft from '@/asset/images/animation/finger/finger_left.png'
import fingerRight from '@/asset/images/animation/finger/finger_right.png'
import fingerSlide from '@/asset/images/animation/finger/finger_slide.png'
import { useAppDispatch, useAppSelector } from '@/store'
import { updateData } from '@/page/earth/earthSlice'
import _ from 'lodash'
import styles from './index.module.scss'

const cn = classNames.bind(styles)

interface IFingerTip {
  position: { x: number; y: number }
  direction: 'left' | 'right' | 'slide'
  text: string
  guideKey: string
}

const defaultConfig: IFingerTip = {
  position: { x: 0, y: 0 },
  direction: 'left',
  text: '',
  guideKey: 'next'
}
const FingerTip: FC<any> = () => {
  const { data } = useAppSelector((state) => state.earth)
  const dispatch = useAppDispatch()
  const imageRef = useRef<HTMLImageElement>(null)

  const { guided } = data
  const [show, setShow] = useState(false)
  const [config, setConfig] = useState<IFingerTip>(defaultConfig)
  const src = useMemo(() => {
    switch (config.direction) {
      case 'left':
        return fingerLeft
      case 'right':
        return fingerRight
      case 'slide':
        return fingerSlide
      default:
        return fingerLeft
    }
  }, [config.direction])

  // 根据图片渲染尺寸计算偏移量
  const calculateOffset = (direction: string, position: { x: number; y: number }) => {
    const { width = 200, height = 108 } = imageRef.current!.getBoundingClientRect()

    if (direction === 'right') {
      return {
        x: (position.x || 0) + width * 0.3, // 60/200 = 0.3
        y: (position.y || 0) - height * 0.37 // 40/108 = 0.37
      }
    } else if (direction === 'left') {
      return {
        x: (position.x || 0) + width * 0.7,
        y: (position.y || 0) - height * 0.37 // 40/108 = 0.37
      }
    } else if (direction === 'slide') {
      return {
        x: (position.x || 0) + width * 0.75, // 150/200 = 0.75
        y: (position.y || 0) - height * 0.24 // 26/108 = 0.24
      }
    }
    return position
  }

  useEffect(() => {
    EventBus.on(Events.fingerTip, (_config: IFingerTip) => {
      const _newConfig = _.cloneDeep(_config)
      // 处理资源偏移量
      _newConfig.position = calculateOffset(_newConfig.direction, _newConfig.position)
      setConfig(_newConfig)
      setTimeout(() => {
        setShow(true)
      }, 100)
    })
    return () => {
      EventBus.off(Events.fingerTip)
    }
  }, [])

  const onDone = () => {
    setShow(false)
    if (config.guideKey) {
      const newGuided = _.cloneDeep(guided)
      dispatch(updateData({
        guided: {
          ...newGuided,
          [config.guideKey]: true
        }
      }))
    }
    setConfig(defaultConfig)
  }

  return (
    <div
      className={cn('fingerTip')}
      onClick={() => {
        onDone()
        setShow(false)
        setConfig(defaultConfig)
      }}
      style={{ left: show ? 0 : '99999rem' }}
    >
      <div className={cn('fingerContent', `${config.direction}`)} style={{ left: config.position.x, top: config.position.y }}>
        <img ref={imageRef} src={src} className={cn('fingerImage')} />
        {config.text && <div className={cn('fingerText')}>{config.text}</div>}
      </div>
    </div>
  )
}

export default FingerTip
