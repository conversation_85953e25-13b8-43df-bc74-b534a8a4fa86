.books-button {
  position: fixed;
  right: 0rem;
  top: 5.625rem;
  z-index: 2;
  &.guided {
    z-index: 999;
  }
  & > button {
    margin: 0rem !important;
  }
  .btn {
    width: 4.5rem;
    height: 4.5625rem;
    cursor: pointer;
    position: relative;
    user-select: none;
  }
}

.books-modal {
  :global {
    .fish-modal-header {
      border-bottom: 0rem;
    }
    .fish-modal-content {
      background: transparent;
    }
    .fish-modal-close {
      display: none;
    }
  }
  .books {
    height: 43.75rem;
    width: 72rem;
    background: url("../../../../../asset/images/books/book_bg.png") no-repeat center center/contain;
    padding: 0.875rem 0rem 1rem 0rem;
    display: flex;
    position: absolute;
    left: 50%;
    transform: translate(-50%);
    .books-tabs {
      width: 4.0625rem;
      height: 100%;
      display: flex;
      flex-direction: column;
      position: relative;
      padding-top: 5.625rem;
      left: -0.625rem;
      user-select: none;
      .books-tab-item {
        writing-mode: vertical-lr;
        color: #8280a3;
        font-family: "Alibaba PuHuiTi";
        font-size: 0.875rem;
        font-style: normal;
        font-weight: 700;
        background: url("../../../../../asset/images/books/book_label_off.png") no-repeat center
          center/contain;
        display: flex;
        align-items: center;
        padding: 1.25rem 0rem;
        position: relative;
        left: -0.25rem;
        top: -0.625rem;

        &.active {
          background: url("../../../../../asset/images/books/book_label_on.png") no-repeat center
            center/contain;
          left: 0rem;
          z-index: 99 !important;
          span {
            background: linear-gradient(178deg, #5b4a4a 0%, #b87234 107.61%);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            position: relative;
            left: -0.25rem;
          }
        }
      }
    }
    .books-content {
      flex: 1;
      height: 100%;
      overflow: hidden;
      padding-left: 4.375rem;
      padding-right: 0.625rem;
      &.reports{
        padding: 0rem;
      }
    }
  }
}
