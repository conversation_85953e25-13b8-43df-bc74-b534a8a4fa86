import { CapacitorConfig } from '@capacitor/cli';

// 远程URL环境配置
const REMOTE_URLS = {
    test: 'https://aic-earth-research.debug.ndaeweb.com/#/earth/home',    
    preproduction: 'https://aic-earth-research.beta.ndaeweb.com/#/earth/home',
    product: 'https://aic-earth-research.sdp.101.com/#/earth/home', 
    sdpsg: 'https://aic-earth-research.sdp.101.com/#/earth/home', 
};

// 获取当前环境的远程URL
const getCurrentRemoteUrl = () => {
    const env = process.env.SDP_ENV || 'test';
    return REMOTE_URLS[env] || REMOTE_URLS.test;
};

const config: CapacitorConfig = {
    appId: 'com.aic.earth.research',
    appName: '地球侦探笔记',
    webDir: 'dist',
    server: {
        url: getCurrentRemoteUrl(),
        androidScheme: 'https',
        cleartext: true,       
        allowNavigation: [
            'localhost',
            '*.localhost',
            '192.168.*',
            '10.0.*',
            '172.16.*',
            '*.101.com',
            '*.ndaeweb.com',
            'aic-earth-research.debug.ndaeweb.com',
            'aic-earth-research.beta.ndaeweb.com',
            'aic-earth-research.sdp.101.com'
        ]
    },
    android: {
        allowMixedContent: true,
        captureInput: true,
        webContentsDebuggingEnabled: true
    },
};

console.log(`🌐 Remote URL Mode - Environment: ${process.env.SDP_ENV}, URL: ${getCurrentRemoteUrl()}`);

export default config;