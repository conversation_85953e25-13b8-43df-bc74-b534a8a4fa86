import {
  useRef, useState, useCallback, useEffect
} from 'react'
import { BidirectionalTTS } from 'byted-ailab-speech-sdk'
import { v4 as uuidv4 } from 'uuid'
import { buildFullUrl } from '../utils/tts'

interface TTSConfig {
  appid: string
  stsToken: string
  speaker?: string
  resourceId?: string
  onPlayComplete?: (val) => void // 播放完成回调
}

interface TTSHookReturn {
  isPlaying: boolean
  audioUrl: string
  startTTS: (config: TTSConfig) => Promise<void>
  sendText: (text: string) => void
  finishSession: () => void
  stopTTS: () => void
  getAudioSrc: () => string | undefined
  getIsConnected: () => boolean
}

const useTTS = (): TTSHookReturn => {
  const [audioUrl, setAudioUrl] = useState('')
  const sendTextRef = useRef<(text: string) => void>()
  const client = useRef(BidirectionalTTS())
  const isConnected = useRef(false)
  const isPlaying = useRef(false)
  const audioElementRef = useRef<HTMLAudioElement>()
  const mediaSourceRef = useRef<MediaSource>()
  const sourceBufferRef = useRef<SourceBuffer>()
  const audioQueueRef = useRef<ArrayBuffer[]>([])
  const isAppendingRef = useRef(false)
  const sessionIdRef = useRef('')
  const tokenRef = useRef('')
  const onPlayCompleteRef = useRef<((val) => void) | undefined>()

  // 初始化和清理
  useEffect(() => () => {
    // 清理音频元素
    if (audioElementRef.current) {
      audioElementRef.current.pause()
      audioElementRef.current = undefined
    }
    // 清理 MediaSource
    if (mediaSourceRef.current && mediaSourceRef.current.readyState === 'open') {
      try {
        mediaSourceRef.current.endOfStream()
      } catch (error) {
        console.warn('Failed to end MediaSource stream:', error)
      }
      mediaSourceRef.current = undefined
    }
    sourceBufferRef.current = undefined
  }, [])

  // 初始化 MediaSource 和音频元素
  const initializeAudioStream = useCallback(async () => {
    try {
      // 创建音频元素
      audioElementRef.current = new Audio()
      audioElementRef.current.preload = 'auto'

      // 检查 MediaSource 支持
      if (!window.MediaSource) {
        console.error('MediaSource is not supported')
        return false
      }

      // 创建 MediaSource
      mediaSourceRef.current = new MediaSource()
      const mediaSourceUrl = URL.createObjectURL(mediaSourceRef.current)
      audioElementRef.current.src = mediaSourceUrl

      // 等待 MediaSource 打开
      await new Promise<boolean>((resolve) => {
        mediaSourceRef.current!.addEventListener('sourceopen', () => {
          try {
            // 创建 SourceBuffer
            sourceBufferRef.current = mediaSourceRef.current!.addSourceBuffer('audio/mpeg')

            sourceBufferRef.current.addEventListener('updateend', () => {
              // 自动播放处理
              if (audioElementRef.current && !isPlaying.current) {
                audioElementRef.current.play().then(() => {
                  isPlaying.current = true
                  console.log('Audio started playing')
                }).catch((error) => {
                  console.error('Failed to play audio:', error)
                })
                audioElementRef.current.addEventListener('ended', () => {
                  onPlayCompleteRef.current?.({ end: true })
                })
              }
            })

            resolve(true)
          } catch (error) {
            console.error('Failed to create SourceBuffer:', error)
            resolve(false)
          }
        })

        mediaSourceRef.current!.addEventListener('error', () => {
          console.error('MediaSource error')
          resolve(false)
        })
      })

      return true
    } catch (error) {
      console.error('Failed to initialize audio stream:', error)
      return false
    }
  }, [])

  // 处理音频队列的函数
  const processAudioQueue = useCallback(async () => {
    if (isAppendingRef.current || audioQueueRef.current.length === 0) return
    if (!sourceBufferRef.current || sourceBufferRef.current.updating) return

    isAppendingRef.current = true

    try {
      while (audioQueueRef.current.length > 0 && !sourceBufferRef.current.updating) {
        const audioBuffer = audioQueueRef.current.shift()!

        // 直接将音频数据追加到 SourceBuffer
        sourceBufferRef.current.appendBuffer(audioBuffer)

        // 等待 updateend 事件，然后继续处理下一个音频块
        await new Promise<void>((resolve) => {
          const onUpdateEnd = () => {
            sourceBufferRef.current!.removeEventListener('updateend', onUpdateEnd)
            resolve()
          }
          sourceBufferRef.current!.addEventListener('updateend', onUpdateEnd)
        })
      }
    } catch (error) {
      console.error('Failed to process audio queue:', error)
    } finally {
      isAppendingRef.current = false
    }
  }, [])

  // 处理音频流的函数
  const appendAudioToStream = useCallback((audioBuffer: ArrayBuffer) => {
    try {
      if (audioBuffer.byteLength === 0) return

      // 将音频数据添加到队列
      audioQueueRef.current.push(audioBuffer)

      // 处理队列
      processAudioQueue()
    } catch (error) {
      console.error('Failed to append audio to stream:', error)
    }
  }, [processAudioQueue])

  const startTTS = useCallback(async (config: TTSConfig): Promise<void> => {
    const {
      appid,
      stsToken,
      speaker = 'zh_female_yingyujiaoyu_mars_bigtts',
      resourceId = 'volc.service_type.10029',
      onPlayComplete
    } = config

    // 保存播放完成回调
    onPlayCompleteRef.current = onPlayComplete

    // 重置状态
    setAudioUrl('')
    audioQueueRef.current = []
    isAppendingRef.current = false
    isConnected.current = false
    isPlaying.current = false

    // 初始化音频流
    const initialized = await initializeAudioStream()
    if (!initialized) {
      console.error('Failed to initialize audio stream')
      throw new Error('Failed to initialize audio stream')
    }

    const auth: Record<string, string> = {}

    return new Promise<void>((resolve, reject) => {
      const connectionTimeout = setTimeout(() => {
        isConnected.current = false
        reject(new Error('TTS connection timeout'))
      }, 10000) // 10秒超时

      try {
        const token = stsToken
        sessionIdRef.current = uuidv4()
        if (token) {
          if (!tokenRef.current) {
            tokenRef.current = token
          }
          auth.api_resource_id = resourceId
          auth.api_app_key = appid
          auth.api_access_key = `Jwt; ${tokenRef.current || token}`
          auth.connectId = sessionIdRef.current
        }

        const fullUrl = buildFullUrl('wss://openspeech.bytedance.com/api/v3/tts/bidirection', auth)
        const _audioUrl = client.current.start({
          debug: true,
          url: fullUrl,
          config: {
            user: {
              uid: 'chat-tts-sdk',
            },
            namespace: 'BidirectionalTTS',
            req_params: {
              speaker,
              audio_params: {
                format: 'mp3',
                sample_rate: 24000,
              }
            },
          },
          onStart: () => {
            console.info('TTS isConnected')
            isConnected.current = true
            clearTimeout(connectionTimeout)
            resolve()
          },
          onMessage: (audioBuffer: ArrayBuffer) => {
            // 处理流式音频数据
            if (audioBuffer.byteLength > 0) {
              appendAudioToStream(audioBuffer)
            }
          },
          onSessionStarted: () => {
            console.log('TTS session started')
          },
          onError: (err) => {
            console.warn('TTS error:', err)
            isConnected.current = false
            isPlaying.current = false
            clearTimeout(connectionTimeout)
            reject(new Error(`TTS error: ${err}`))
          },
          onClose: () => {
            isConnected.current = false
            isPlaying.current = false
          },
          onTTSSentenceStart(val) {
            console.info('TTS sentence start:', val)
          },
          onTTSSentenceEnd(val) {
            console.info('TTS sentence end:', val)
            onPlayCompleteRef.current?.(val)
          },
        })
        setAudioUrl(_audioUrl)

        // 设置发送文本的引用
        sendTextRef.current = (text: string) => {
          if (client.current && isConnected.current) {
            console.info('Sending TTS text:', text)
            client.current.sendText(text)
          }
        }
      } catch (error) {
        console.error('TTS init failed:', error)
        isConnected.current = false
        clearTimeout(connectionTimeout)
        reject(new Error(`TTS init failed: ${error}`))
      }
    })
  }, [appendAudioToStream, initializeAudioStream])

  const sendText = useCallback((text: string) => {
    if (sendTextRef.current && isConnected.current) {
      sendTextRef.current(text)
    }
  }, [isConnected.current])

  const finishSession = useCallback(() => {
    if (client.current && isConnected) {
      client.current.finishSession()
      mediaSourceRef.current?.endOfStream()
    }
  }, [isConnected])

  const stopTTS = useCallback(() => {
    console.log('sessionIdRef', sessionIdRef.current)
    // 停止 TTS 连接和播放
    isConnected.current = false
    isPlaying.current = false
    isAppendingRef.current = false

    // 清空音频队列
    audioQueueRef.current = []

    // 停止音频播放
    if (audioElementRef.current) {
      audioElementRef.current.pause()
      audioElementRef.current.currentTime = 0
    }

    // 清理 MediaSource
    if (mediaSourceRef.current && mediaSourceRef.current.readyState === 'open') {
      try {
        mediaSourceRef.current.endOfStream()
      } catch (error) {
        console.warn('Failed to end MediaSource stream:', error)
      }
    }

    // 重置引用
    sourceBufferRef.current = undefined
    mediaSourceRef.current = undefined
    audioElementRef.current = undefined
    onPlayCompleteRef.current = undefined
  }, [])

  const getAudioSrc = useCallback(() => audioElementRef.current?.src || undefined, [])

  const getIsConnected = useCallback(() => isConnected.current, [])

  return {
    isPlaying: isPlaying.current,
    audioUrl,
    startTTS,
    sendText,
    finishSession,
    stopTTS,
    getAudioSrc,
    getIsConnected
  }
}

export default useTTS
