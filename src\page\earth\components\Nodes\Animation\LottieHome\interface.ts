interface FrameSegment {
  frames: [number, number];
  loop?: boolean;
}

interface LottieParam {
  type: 'text' | 'image';
  value: string;
  align?: 'center';
  props?: Record<string, any>;
}

export interface LottiePlayerParams {
  /** Lottie区分id @example 'key' */
  key: string;

  /** Lottie地址 @example 'url' */
  url?: string;

  /** Lottie JSON对象 */
  json?: object;

  /** Lottie 音频资源 */
  audio?: string;

  /** Lottie 音频音量 */
  volume?: number;

  /** Lottie 是否循环 */
  loop?: boolean;

  /** Lottie 显示位置left @example '0' */
  left?: number | string;

  /** Lottie 显示位置top @example '0' */
  top?: number | string;

  /** Lottie 显示宽度 @example '0' */
  width?: number | string;

  /** Lottie 显示高度 @example '0' */
  height?: number | string;

  /** Lottie 全局层级 @example 1 */
  zIndex?: number;

  /** Lottie 开始播放的时间，单位ms @example 'seek' */
  seek?: number;

  /** Lottie 替换参数 @example '{"#Text": {"type": "text", "value": "Hello"}}}' */
  params?: Record<string, LottieParam>;

  /** Lottie 片段时长 @example 0 */
  totalTime?: number;

  /** Lottie 片段设置 @example '[]' */
  frameSegments?: FrameSegment[];
}

// 默认值实现（如需使用默认值可单独实现）
export const defaultLottiePlayerParams: LottiePlayerParams = {
  key: '',
  url: '',
  left: 0,
  top: 0,
  width: '100%',
  height: '100%',
  zIndex: 0,
  seek: 0,
  params: {},
  totalTime: 0,
  frameSegments: [],
}
