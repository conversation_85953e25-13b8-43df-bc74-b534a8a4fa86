apply plugin: 'com.android.application'
apply plugin: 'io.sentry.android.gradle'

android {
    // ndkVersion '21.4.7075529'
    compileSdk rootProject.ext.compileSdk
    
    defaultConfig {
        applicationId "com.aic.earth.research"
        minSdk rootProject.ext.minSdk
        targetSdk rootProject.ext.targetSdk
        versionCode rootProject.ext.versionCode
        versionName rootProject.ext.versionName
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        aaptOptions {
             // Files and dirs to omit from the packaged assets dir, modified to accommodate modern web apps.
             // Default: https://android.googlesource.com/platform/frameworks/base/+/282e181b58cf72b6ca770dc7ca5f91f135444502/tools/aapt/AaptAssets.cpp#61
            ignoreAssetsPattern '!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~'
        }
    }
    
    // Signing configurations
    signingConfigs {
        debug {
            // Use default debug keystore
            storeFile file('debug.keystore')
            storePassword 'android'
            keyAlias 'androiddebugkey'
            keyPassword 'android'
        }
        release {
            storeFile file('aic-earth-research.keystore')
            storePassword 'aicearth123'
            keyAlias 'aic-earth-research'
            keyPassword 'aicearth123'
        }
    }
    buildTypes {
        debug {
            applicationIdSuffix '.debug'
            debuggable true
            minifyEnabled false
            // Use debug signing
            signingConfig signingConfigs.debug
        }
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            // Use release signing
            signingConfig signingConfigs.release
        }
    }
}

repositories {
    flatDir{
        dirs 'libs'
    }
}

dependencies {
    implementation fileTree(include: ['*.jar'], dir: 'libs')
    implementation "androidx.appcompat:appcompat:$androidxAppCompatVersion"
    implementation project(':capacitor-android')
    testImplementation "junit:junit:$junitVersion"
    androidTestImplementation "androidx.test.ext:junit:$androidxJunitVersion"
    androidTestImplementation "androidx.test.espresso:espresso-core:$androidxEspressoCoreVersion"
    implementation 'com.google.code.gson:gson:2.10.1'
}

apply from: 'capacitor.build.gradle'

try {
    def servicesJSON = file('google-services.json')
    if (servicesJSON.text) {
        apply plugin: 'com.google.gms.google-services'
    }
} catch(Exception e) {
    logger.info("google-services.json not found, google-services plugin not applied. Push Notifications won't work")
}
