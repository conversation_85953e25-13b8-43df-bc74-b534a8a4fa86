import 'dart:convert';

import 'package:crypto/crypto.dart';
import 'package:dio/dio.dart';
import 'package:grey_publish/platform_info.dart';
import 'package:ppt101_uc_utils/uc_config.dart';
import 'package:ppt101_uc_utils/uc_utils.dart';

Dio dio = Dio();
PlatformInfo _platformInfo = PlatformInfo();
String greyUrl = "https://publish-mng.sdp.101.com";
Map<String, dynamic> commonHeaders = {
  "Accept": "application/json", //must input
  "Content-Type": "application/json", //must input
  "sdp-app-id": _platformInfo.sdpAppId
};

UcContextModel model = getProductUcContextModel();
UcContext context = UcContext(model);

Future<UcTokenModel?> login(String username, String md5Password) async {
  UcApi ucApi = UcApi(context);
  UcSessionModel? session = await ucApi.createSession();
  if (session == null) {
    print("session is null");
    return null;
  }

  UcTokenModel? token = await ucApi.createToken(
      session: session,
      accountType: 'org',
      loginNameType: 'org_user_code',
      orgCode: "ND",
      loginName: username,
      md5Password: md5Password);

  if (token == null) {
    print("token is null");
    return null;
  }

  return token;
}

String md5Convert(String data) {
  final bytes = utf8.encode(data);
  final buffer = bytes.toList();
  buffer.addAll([0xa3, 0xac, 0xa1, 0xa3]);
  buffer.addAll(utf8.encode('fdjf,jkgfkl'));
  return md5.convert(buffer).toString();
}

Future<int?> createVersion(
    {required UcTokenModel token,
    required String version,
    required int versionCode,
    required String description,
    required int releaseDate,
    required String extra}) async {
  print("createVersion");
  Uri uri = Uri.parse("$greyUrl/v0.1/m/versions");
  print("url ${uri.toString()}");

  Map<String, dynamic> headers = Map.from(commonHeaders);
  headers["Authorization"] = context.generateAuthorization(
      method: "POST", host: uri.host, path: uri.path, token: token);
  print("headers $headers");

  Map data = {
    "version": version, // 必填, 版本名称, ******* ~ 99.99.99.999
    "version_code": versionCode, // 必填, 唯一，版本号, 必须比上一个版本号大
    "description": description, // 必填, 描述, max 50
    "release_date": releaseDate, // 必填，发布日期
    "extra": extra
  };
  print("data: $data");
  Response<Map<String, dynamic>> response = await dio.post(
    uri.toString(),
    data: data,
    options: Options(headers: headers, validateStatus: (_) => true),
  );
  print("response $response");
  return response.data?["id"];
}

Future<int?> createTask(
    {required UcTokenModel token,
    required String taskName,
    required int versionId}) async {
  print("createTask");
  Uri uri = Uri.parse("$greyUrl/v0.1/m/grayscale_tasks");
  print("url ${uri.toString()}");

  Map<String, dynamic> headers = Map.from(commonHeaders);
  headers["Authorization"] = context.generateAuthorization(
      method: "POST", host: uri.host, path: uri.path, token: token);
  print("headers $headers");

  DateTime now = DateTime.now();

  num startTime = now.add(Duration(minutes: 5)).millisecondsSinceEpoch;
  num endTime = now.add(Duration(days: 365)).millisecondsSinceEpoch;
  Map data = {
    "name": taskName, //任务名称 必填 max（20）
    "version_id": versionId, //必填 版本id
    "publish_type": 1, //1局部发布，2全网发布 必填
    "user_group_id":
        _platformInfo.userGroupId, //局部发布必填 用户组id; 全网发布选填，有值表示排除该用户组
    "max_update": 1500, //局部发布必填 最大升级量
    "start_time": startTime, //局部发布必填 发布时间
    "end_time": endTime, //局部发布必填 结束时间
    "push_type": 1, //  是否马上推送 0:false, 1: true
  };
  print("data: $data");

  Response<Map<String, dynamic>> response = await dio.post(
    uri.toString(),
    data: data,
    options: Options(headers: headers, validateStatus: (_) => true),
  );
  print("response $response");
  return response.statusCode;
}

String jsonVersionInfo(
    int versionCode, String versionName, String downloadUrl, String updateLog) {
  // minsSysVersion 为ios端限制的最低版本号
  return """{
	"versionCode": $versionCode,
	"versionName": "$versionName",
	"minsdk": 26,
  "minsSysVersion": "12.0",
	"downloadUrl": "$downloadUrl",
	"forceUpdate":false,
	"updateLog": "$updateLog"
}""";
}
