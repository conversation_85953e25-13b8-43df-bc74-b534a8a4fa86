package com.aic.earth.research;

import android.Manifest;
import android.app.DownloadManager;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.os.Environment;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.widget.Toast;

import com.google.gson.Gson;
import com.google.gson.annotations.SerializedName;

import org.json.JSONObject;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class UpdateChecker {
    private final String sdpAppId = "43063c1a-1106-40ae-acce-c756e0e03e7c";
    private final String checkUrl = "https://publish-mng.sdp.101.com/v0.1/visitor/grayscale_tasks";
    private final Context context;
    // 添加轮询控制
    private Handler pollHandler;
    private Runnable pollRunnable;
    private volatile boolean isPolling = false;
    private long pollStartTime = 0;
    private static final long MAX_POLL_DURATION = 30 * 60 * 1000; // 最大轮询30分钟

    public UpdateChecker(Context context) {
        this.context = context;

        checkAndCleanupApk(context);
    }

    public void checkForUpdate() {
        ExecutorService executor = Executors.newSingleThreadExecutor();
        Handler handler = new Handler(Looper.getMainLooper());
        executor.execute(() -> {
            RemoteVersion remoteVersion = featVersion();
            if (remoteVersion != null) {
                handler.post(() -> handleUpdateResult(remoteVersion));
            }
        });
    }

    private HttpURLConnection createUpdateConnection(String urlString) throws Exception {
        URL url = new URL(urlString);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        conn.setRequestMethod("GET");
        conn.setConnectTimeout(5000);
        conn.setReadTimeout(5000);
        conn.setRequestProperty("Accept", "application/json");
        conn.setRequestProperty("Content-Type", "application/json");
        conn.setRequestProperty("sdp-app-id", sdpAppId);
        return conn;
    }

    private RemoteVersion featVersion() {
        String macCode = "0";
        String versionId = "0";

        try {
            String urlString = checkUrl + "?mac_code=" + macCode + "&version_id=" + versionId
                    + "&level_two_channel_id=1";
            HttpURLConnection conn = createUpdateConnection(urlString);
            int responseCode = conn.getResponseCode();
            if (responseCode == 200) {
                BufferedReader reader = new BufferedReader(new InputStreamReader(conn.getInputStream()));
                StringBuilder sb = new StringBuilder();
                String line;
                while ((line = reader.readLine()) != null) {
                    sb.append(line);
                }
                reader.close();
                String result = sb.toString();
                // {"create_time":1742182559000,"biz_type":"","version_code":1258,"description":"1.7.28.258","version":"1.7.28.258","update_time":1742208420000,"release_date":1742208420000,"enable":1,"extra":"{\n\t\"versionCode\":
                // 1258,\n\t\"versionName\": \"1.7.28.258\",\n\t\"minsdk\": 21,\n
                // \"minsSysVersion\": \"12.0\",\n\t\"downloadUrl\":
                // \"https://gcdncs.101.com/v0.1/static/ppt/karaoke_android/karaoke_release_1.7.28.258_341.apk\",\n\t\"forceUpdate\":false,\n\t\"updateLog\":
                // \"1.
                // 原唱播放改为干声+伴奏形式\"\n}","id":221,"state":1,"grayscale_task_id":258,"usedCount":2,"pkg_type":0}
                Log.d("UpdateChecker", "Update check result: " + result);
                JSONObject json = new JSONObject(result);
                if (json.has("extra")) {
                    String extra = json.getString("extra");
                    return RemoteVersion.fromJson(extra);
                }
            }
        } catch (Exception e) {
            Log.e("UpdateChecker", "Update check failed", e);
        }
        return null;
    }

    private void handleUpdateResult(RemoteVersion result) {
        if (result == null) {
            Log.d("UpdateChecker", "No update available or result is empty");
            return;
        }
        try {
            int currentVersionCode = getCurrentVersionCode();
            Log.d("UpdateChecker", "currentVersionCode:" + currentVersionCode + ", Update info: " + result);
            if (result.versionCode > currentVersionCode) {
                showUpdateDialog(result, currentVersionCode);
            }
        } catch (Exception e) {
            Log.e("UpdateChecker", "Parse update info failed", e);
        }
    }

    private int getCurrentVersionCode() {
        try {
            PackageManager pm = context.getPackageManager();
            PackageInfo pi = pm.getPackageInfo(context.getPackageName(), 0);
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.P) {
                return (int) pi.getLongVersionCode();
            } else {
                return pi.versionCode;
            }
        } catch (Exception e) {
            return 0;
        }
    }

    private void showUpdateDialog(RemoteVersion result, int currentVersionCode) {
        android.app.Dialog dialog = new android.app.Dialog(context);
        android.view.View view = android.view.LayoutInflater.from(context).inflate(
                R.layout.dialog_update, null);
        dialog.setContentView(view);
        dialog.setCancelable(false);
        // 设置宽度为屏幕宽度的 60%
        if (dialog.getWindow() != null) {
            android.view.Window window = dialog.getWindow();
            android.view.WindowManager.LayoutParams lp = window.getAttributes();
            android.util.DisplayMetrics dm = context.getResources().getDisplayMetrics();
            if (dm.widthPixels > dm.heightPixels) {
                lp.width = (int) (dm.widthPixels * 0.6f);
            } else {
                lp.width = (int) (dm.widthPixels * 0.9f);
            }
            window.setAttributes(lp);
        }

        android.widget.TextView titleView = view.findViewById(R.id.update_title);
        android.widget.TextView messageView = view.findViewById(R.id.update_message);
        android.widget.Button btnCancel = view.findViewById(R.id.update_cancel);
        android.widget.Button btnConfirm = view.findViewById(R.id.update_confirm);
        if (titleView != null) {
            String title = context.getString(R.string.update_dialog_title, result.versionName);
            titleView.setText(title);
        }
        if (messageView != null) {
            messageView.setText(result.updateLog);
        }
        btnCancel.setOnClickListener(v -> dialog.dismiss());
        btnConfirm.setOnClickListener(v -> {
            startDownloadAndPoll(result); // 传递整个 result 对象
            dialog.dismiss();
        });
        dialog.show();
    }

    private void startDownloadAndPoll(RemoteVersion version) {
        if (version == null || version.downloadUrl == null || version.downloadUrl.isEmpty()) {
            Toast.makeText(context, "下载地址无效", Toast.LENGTH_SHORT).show();
            return;
        } // 使用版本号生成唯一文件名，例如 "app_name_1.2.3.apk"
        final String apkName = context.getString(R.string.app_name) + "_" + version.versionName + ".apk";

        // 确保下载目录中的旧文件被删除，避免安装旧版本
        java.io.File apkFile = new java.io.File(
                Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS), apkName);
        if (apkFile.exists()) {
            try {
                apkFile.delete();
            } catch (Exception e) {
            }
        }

        DownloadManager.Request request = new DownloadManager.Request(Uri.parse(version.downloadUrl));
        request.setTitle(context.getString(R.string.app_name));
        request.setDescription("正在下载更新 " + version.versionName);
        request.setNotificationVisibility(DownloadManager.Request.VISIBILITY_VISIBLE_NOTIFY_COMPLETED);
        request.setDestinationInExternalPublicDir(Environment.DIRECTORY_DOWNLOADS, apkName);
        request.setMimeType("application/vnd.android.package-archive");

        DownloadManager dm = (DownloadManager) context.getSystemService(Context.DOWNLOAD_SERVICE);
        if (dm == null) {
            Toast.makeText(context, "无法初始化下载服务", Toast.LENGTH_SHORT).show();
            return;
        }

        long downloadId = dm.enqueue(request);
        Toast.makeText(context, "开始下载新版本...", Toast.LENGTH_SHORT).show();

        // 保存清理信息（包含APK文件名、版本号和下载ID）
        saveCleanupInfo(apkName, version.versionCode, downloadId);

        // 启动轮询检查下载状态
        pollDownloadStatus(dm, downloadId, apkName);
    }

    private void pollDownloadStatus(final DownloadManager dm, final long downloadId, final String apkName) {
        // 如果已在轮询，先停止之前的轮询
        stopPolling();

        isPolling = true;
        pollStartTime = System.currentTimeMillis();
        pollHandler = new Handler(Looper.getMainLooper());
        pollRunnable = new Runnable() {
            @Override
            public void run() {
                // 检查是否应该停止轮询
                if (!isPolling) {
                    Log.d("UpdateChecker", "轮询已停止");
                    return;
                }

                // 检查超时保护（避免无限轮询）
                if (System.currentTimeMillis() - pollStartTime > MAX_POLL_DURATION) {
                    Log.w("UpdateChecker", "轮询超时，自动停止");
                    Toast.makeText(context, "下载时间过长，请检查网络连接", Toast.LENGTH_SHORT).show();
                    stopPolling();
                    return;
                }

                DownloadManager.Query query = new DownloadManager.Query();
                query.setFilterById(downloadId);
                android.database.Cursor cursor = null;
                try {
                    cursor = dm.query(query);
                    if (cursor != null && cursor.moveToFirst()) {
                        int status = cursor.getInt(cursor.getColumnIndexOrThrow(DownloadManager.COLUMN_STATUS));
                        switch (status) {
                            case DownloadManager.STATUS_SUCCESSFUL:
                                Log.d("UpdateChecker", "轮询到下载成功: " + downloadId);
                                installApk(dm, downloadId, apkName);
                                stopPolling(); // 成功，结束轮询
                                return;

                            case DownloadManager.STATUS_FAILED:
                                Log.e("UpdateChecker", "轮询到下载失败: " + downloadId);
                                Toast.makeText(context, "下载失败", Toast.LENGTH_SHORT).show();
                                dm.remove(downloadId); // 清理失败的任务
                                stopPolling(); // 失败，结束轮询
                                return;

                            case DownloadManager.STATUS_PENDING:
                            case DownloadManager.STATUS_RUNNING:
                            case DownloadManager.STATUS_PAUSED:
                                // 下载中，1秒后继续轮询
                                if (isPolling && pollHandler != null) {
                                    pollHandler.postDelayed(this, 1000);
                                }
                                break;
                        }
                    } else {
                        // 查询不到任务，可能已被用户手动取消
                        Log.w("UpdateChecker", "查询不到下载任务: " + downloadId);
                        stopPolling();
                    }
                } finally {
                    if (cursor != null) {
                        cursor.close();
                    }
                }
            }
        };

        pollHandler.post(pollRunnable);
    }

    private void installApk(DownloadManager dm, long downloadId, String apkName) {
        Uri uri = dm.getUriForDownloadedFile(downloadId);
        if (uri == null) {
            Log.e("UpdateChecker", "无法获取下载文件的URI");
            Toast.makeText(context, "安装失败：无法找到文件", Toast.LENGTH_SHORT).show();
            return;
        }

        Intent install = new Intent(Intent.ACTION_VIEW);
        install.setDataAndType(uri, "application/vnd.android.package-archive");
        install.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        install.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
        context.startActivity(install);

        // 不再立即删除APK，等待应用重启后检查版本号再删除
        Log.d("UpdateChecker", "启动安装程序，APK将在下次应用启动时清理");
    } // 保存清理信息到 SharedPreferences（包含APK文件名、版本号和下载ID）

    private void saveCleanupInfo(String apkName, int targetVersionCode, long downloadId) {
        android.content.SharedPreferences prefs = context.getSharedPreferences("UpdateCleanup", Context.MODE_PRIVATE);
        prefs.edit()
                .putString("pending_apk", apkName)
                .putInt("target_version", targetVersionCode)
                .putLong("download_id", downloadId)
                .apply();
        Log.d("UpdateChecker", "保存清理信息: " + apkName + ", 目标版本: " + targetVersionCode + ", 下载ID: " + downloadId);
    }// 提供静态方法给 MainActivity 调用，直接清理安装包

    public static void checkAndCleanupApk(Context context) {
        android.content.SharedPreferences prefs = context.getSharedPreferences("UpdateCleanup", Context.MODE_PRIVATE);
        String pendingApk = prefs.getString("pending_apk", null);
        long downloadId = prefs.getLong("download_id", -1);

        if (pendingApk == null) {
            return; // 没有待清理的APK
        }

        // 直接删除APK文件，不进行版本检测
        try {
            java.io.File apkFile = new java.io.File(
                    Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS), pendingApk);
            if (apkFile.exists()) {
                if (apkFile.delete()) {
                    Log.d("UpdateChecker", "成功删除安装包: " + pendingApk);
                } else {
                    Log.w("UpdateChecker", "删除安装包失败: " + pendingApk);
                }
            } else {
                Log.d("UpdateChecker", "安装包已不存在: " + pendingApk);
            }
        } catch (Exception e) {
            Log.e("UpdateChecker", "删除安装包时发生错误: " + pendingApk, e);
        }

        // 清理 DownloadManager 中的下载记录
        if (downloadId != -1) {
            try {
                DownloadManager dm = (DownloadManager) context.getSystemService(Context.DOWNLOAD_SERVICE);
                if (dm != null) {
                    int removedCount = dm.remove(downloadId);
                    if (removedCount > 0) {
                        Log.d("UpdateChecker", "成功清除DownloadManager记录: " + downloadId);
                    } else {
                        Log.w("UpdateChecker", "DownloadManager记录可能已不存在: " + downloadId);
                    }
                }
            } catch (Exception e) {
                Log.e("UpdateChecker", "清除DownloadManager记录时发生错误: " + downloadId, e);
            }
        }

        // 清除清理信息
        prefs.edit().clear().apply();
        Log.d("UpdateChecker", "已清除更新清理信息");
    }

    public static class RemoteVersion {
        @SerializedName("versionCode")
        public int versionCode = 0;
        @SerializedName("versionName")
        public String versionName;
        @SerializedName("downloadUrl")
        public String downloadUrl;
        @SerializedName("updateLog")
        public String updateLog;
        @SerializedName("minIosSysVersion")
        public String minIosSysVersion;
        @SerializedName("minSdkVersion")
        public int minSdkVersion = 26;
        @SerializedName("forceUpdate")
        public boolean forceUpdate = false;

        public static RemoteVersion fromJson(String jsonStr) {
            return new Gson().fromJson(jsonStr, RemoteVersion.class);
        }
    }

    /**
     * 停止下载状态轮询
     * 防止内存泄漏和不必要的后台任务
     */
    public void stopPolling() {
        isPolling = false;
        if (pollHandler != null && pollRunnable != null) {
            pollHandler.removeCallbacks(pollRunnable);
            Log.d("UpdateChecker", "已停止下载状态轮询");
        }
        pollHandler = null;
        pollRunnable = null;
    }

    /**
     * 检查是否正在轮询
     */
    public boolean isPolling() {
        return isPolling;
    }
}
