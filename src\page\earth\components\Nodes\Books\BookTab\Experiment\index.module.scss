.experiment {
  height: 100%;
  display: flex;
  .experiment-list {
    overflow: auto;
    flex: 1;
    height: 100%;

    .experiment-title-box {
      margin: 2.1875rem 0rem 0rem 1.875rem;
      display: flex;
      .experiment-title {
        font-family: "Alibaba PuHuiTi";
        font-size: 1.5rem;
        font-style: normal;
        font-weight: 700;
        color: #655c5c;
        margin-right: 2.75rem;
        position: relative;
        text-shadow: 0 0.1875rem 0 #fff;
        .experiment-key {
          position: absolute;
          left: 0rem;
          top: 2.1875rem;
          width: 5.625rem;
        }
      }
      .experiment-des {
        flex: 1;
        font-size: 0.875rem;
        font-style: normal;
        font-weight: 400;
        position: relative;
        img {
          position: absolute;
          top: 1.375rem;
          right: 2.5rem;
          width: 100%;
        }
      }
    }
    .experiment-content {
      padding: 1.25rem;
      display: flex;
      flex-direction: column;
      height: calc(100% - 4.5rem);
      .tab-list {
        display: flex;
        overflow-x: auto;
        min-width: 0;
        white-space: nowrap;
        position: relative;
        margin-bottom: 0.625rem;
        .header-bottom {
          position: absolute;
          width: 100%;
          bottom: 0rem;
        }
        &::-webkit-scrollbar {
          display: none;
        }
        -ms-overflow-style: none;
        scrollbar-width: none;

        .tab-item {
          height: 2rem;
          line-height: 2rem;
          width: 8.75rem;
          flex-shrink: 0;
          color: #fff;
          font-family: "Alibaba PuHuiTi";
          font-size: 0.875rem;
          font-style: normal;
          font-weight: 500;
          text-align: center;
          position: relative;
          background: url("../../../../../../../asset/images/books/hypothesis/target_imgs/tab_c_off.png")
            no-repeat center center / contain;

          &.first {
            background: url("../../../../../../../asset/images/books/hypothesis/target_imgs/tab_l_off.png")
              no-repeat center center / contain;
            &.active {
              background: url("../../../../../../../asset/images/books/hypothesis/target_imgs/tab_l_on.png")
                no-repeat center center / contain;
            }
          }
          &.active {
            background: url("../../../../../../../asset/images/books/hypothesis/target_imgs/tab_c_on.png")
              no-repeat center center / contain;
            z-index: 99 !important;
          }
        }
      }
    }
  }
  .experiment-form {
    flex: 1;
    padding: 0.625rem;
    height: 100%;
    display: flex;
    flex-direction: column;
    .experiment-title-box {
      margin: 1.5625rem 0rem 0rem 1.25rem;
      display: flex;
      margin-bottom: 1.25rem;
      .experiment-title {
        font-family: "Alibaba PuHuiTi";
        font-size: 1.5rem;
        font-style: normal;
        font-weight: 700;
        color: #655c5c;
        margin-right: 2.75rem;
        position: relative;
        text-shadow: 0 0.1875rem 0 #fff;
        .experiment-key {
          position: absolute;
          left: 0rem;
          top: 2.1875rem;
          width: 5.625rem;
        }
      }
      .experiment-des {
        flex: 1;
        font-size: 0.875rem;
        font-style: normal;
        font-weight: 400;
        position: relative;
        img {
          position: absolute;
          top: 1.375rem;
          right: 2.5rem;
          width: 100%;
        }
      }
    }
    .experiment-from-content {
      flex: 1;
      background: url("../../../../../../../asset/images/books/experiment/reflection_line.png")
        no-repeat center center / contain;
      padding: 0.625rem 1.25rem;
      .block-w {
        position: relative;
        width: 100%;
        height: 13.75rem;
        margin-top: 1.875rem;
        background: url("../../../../../../../asset/images/books/experiment/reflection_input_bg.png")
          no-repeat center center / contain;
        & + .block-w {
          margin-top: 3.75rem;
        }
        .w-img {
          position: absolute;
          z-index: 2;
          width: 12.5rem;
          left: -0.625rem;
          top: -1.5625rem;
        }
        .w-icon {
          position: absolute;
          bottom: 0.3125rem;
          right: 1.25rem;
          width: 2.5rem;
        }
        :global {
          .fish-input-textarea-container {
            position: absolute;
            width: calc(100% - 2.5rem);
            left: 1.25rem;
            top: 1.25rem;
            .fish-input {
              background: transparent;
              border: unset;
              box-shadow: unset;
              color: #664b48;
              font-size: 1rem;
            }
          }
        }
      }
    }
  }
}

.experiment-item {
  position: relative;
  background: url("../../../../../../../asset/images/books/experiment/experiment_bg.png") no-repeat
    center center;
  background-size: 100% 100%;
  flex: 1;
  padding: 1.25rem;
  .item-group {
    .item-title {
      color: #664b48;
      font-family: "Alibaba PuHuiTi";
      font-size: 1rem;
      font-style: normal;
      font-weight: 500;
      position: relative;
      display: inline-block;
      margin-bottom: 0.75rem;
      span {
        z-index: 2;
        position: relative;
      }
      img {
        z-index: 1;
        width: 100%;
        position: absolute;
        bottom: 0rem;
        left: 0rem;
      }
    }
    .item-subtitle {
      color: #847674;
      font-family: "Alibaba PuHuiTi";
      font-size: 1rem;
      font-style: normal;
      font-weight: 500;
    }

    & + img {
      margin: 0.75rem 0rem;
      width: 100%;
    }
  }
  .result {
    position: absolute;
    right: 3.125rem;
    transform: rotateZ(350deg);
    z-index: 999;
    top: -1.875rem;
    width: 8.75rem;
    pointer-events: none;
  }
  :global {
    table {
      border-radius: 0.3125rem;
    }
    .fish-table-thead {
      th {
        background-color: rgba(0, 0, 0, 0.8);
        color: #fff;
        padding: 0.3125rem 0.5rem;
        font-weight: bold;
        font-size: 0.8125rem;
      }
    }
    .fish-table-tbody {
      td {
        padding: 0.625rem;
        background-color: #fff;
      }
    }
    tr,
    th,
    td {
      border-right-color: rgba(0, 0, 0, 0.4) !important;
      border-bottom-color: rgba(0, 0, 0, 0.4) !important;
      user-select: none;
    }
    td:first-child {
      border-left: 0.0625rem solid rgba(0, 0, 0, 0.4) !important;
    }
    tr:last-child {
      td:first-child {
        border-bottom-left-radius: 0.3125rem;
      }
      td:last-child {
        border-bottom-right-radius: 0.3125rem;
      }
    }
  }
}
