.screenshotCard {
  overflow: hidden;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 99999;
  text-overflow: ellipsis;
  width: 18.75rem;
  height: 15rem;
  text-align: center;
  background: url("../../../../../../asset/images/books/hypothesis/source_imgs/clue_photo_on.png")
    no-repeat center center / contain;
  .screenshotContent {
    max-width: 100%;
    max-height: 100%;
    position: relative;
    width: 16.5625rem;
    height: 9.375rem;
    margin: 1.375rem 0rem 0rem 1.0625rem;

    img,
    span {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      z-index: 1;
    }
    span {
      z-index: 2;
      color: #fff;
      overflow: hidden;
      text-align: left;
      padding: 0.625rem;
    }
  }

  .source-des {
    width: 12.5rem;
    height: 1.875rem;
    margin: 0rem auto;
    font-size: 0.75rem;
    color: #666;
    margin-top: 0.3125rem;
    word-break: break-word;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    padding: 0rem 1.5rem;
  }
}
