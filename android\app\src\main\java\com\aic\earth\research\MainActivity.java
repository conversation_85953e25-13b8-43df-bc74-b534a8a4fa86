package com.aic.earth.research;

import android.os.Bundle;
import android.webkit.WebView;
import android.widget.Button;
import android.widget.FrameLayout;
import android.view.ViewGroup;
import android.view.View;
import com.getcapacitor.BridgeActivity;
import io.sentry.Sentry;

public class MainActivity extends BridgeActivity {
    private UpdateChecker updateChecker;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // Always enable WebView debugging for development
        // This can be controlled by build variants later
        WebView.setWebContentsDebuggingEnabled(true);

        updateChecker = new UpdateChecker(this);
        // 应用启动时检测更新
        updateChecker.checkForUpdate();

        // testCrashButton();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (updateChecker != null) {
            updateChecker.stopPolling();
        }
    }

    /**
     * 测试崩溃
     */
    void testCrashButton() {
        // 添加测试按钮
        Button crashButton = new Button(this);
        crashButton.setText("测试崩溃");
        crashButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // 主动上报
                // Sentry.captureException(new RuntimeException("This app uses Sentry!:)"));

                /// 被动上报
                throw new RuntimeException("Test crash for Sentry"); // 被动
            }
        });

        // 将按钮添加到 WebView 之上
        FrameLayout rootView = (FrameLayout) getWindow().getDecorView().findViewById(android.R.id.content);
        FrameLayout.LayoutParams params = new FrameLayout.LayoutParams(
                ViewGroup.LayoutParams.WRAP_CONTENT,
                ViewGroup.LayoutParams.WRAP_CONTENT);
        params.topMargin = 100; // 距顶部 100px，可根据需要调整
        params.leftMargin = 50; // 距左侧 50px，可根据需要调整
        rootView.addView(crashButton, params);

    }
}
