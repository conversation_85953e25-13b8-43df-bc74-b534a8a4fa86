import React, { FC, useMemo, useState } from 'react'
import classNames from 'classnames/bind'
import reflectionReport from '@/asset/images/books/reports/reflection_report.png'
import checkIcon from '@/asset/images/books/reports/report_check.png'
import defaultLeftPng from '@/asset/images/books/reports/default_left.png'
import defaultRightPng from '@/asset/images/books/reports/default_right.png'
import activeLeftPng from '@/asset/images/books/reports/active_left.png'
import activeRightPng from '@/asset/images/books/reports/active_right.png'
import checkIconPng from '@/asset/images/books/reports/check_icon.png'
import { IEarthData } from '@/page/earth/interface'
import { Popover } from 'fish'
import styles from './index.module.scss'
import { CHECK_LIST } from '../../constant'

const cn = classNames.bind(styles)

interface IReportsProps {
  data: IEarthData['books']
}
const Reports: FC<IReportsProps> = ({
  data,
}) => {
  const [active, setActive] = useState('')

  const countList = useMemo(() => {
    const questionCount = data.hypothesis.question_presumes.length
    const experimentCount = data.experiment?.length
    const clueCount = data.hypothesis.clue.length
    const presumeCount = data.hypothesis.presumes.length
    const list = [
      {
        label: `你提出了${questionCount}个问题`,
        key: 'question',
        placement: 'leftBottom',
        icon: 'left'
      },
      {
        label: `你收集了${clueCount}个线索`,
        key: 'clue',
        placement: 'rightBottom',
        icon: 'right'
      },
      {
        label: `你做了${experimentCount}次模拟实验`,
        key: 'experiment',
        placement: 'leftBottom',
        icon: 'left'
      },
      {
        label: `你迭代了${presumeCount}版假设`,
        key: 'presume',
        placement: 'right',
        icon: 'right'
      },
      {
        label: '你得出了实验结论',
        key: 'conclusion',
        placement: 'leftTop',
        icon: 'left'
      },
    ]
    return list
  }, [data])

  const activeDirection = useMemo(() => {
    if (active === 'clue' || active === 'presume') {
      return 'right'
    } else if (active === 'question' || active === 'experiment' || active === 'conclusion') {
      return 'left'
    }
    return ''
  }, [active])

  const renderContent = (key) => {
    if (key === 'question') {
      return data.hypothesis.question_presumes.map((item, index) => (
        <div className={cn('detail-item')}>
          <div className={cn('item-label')}>{`问题${index + 1}`}</div>
          <div className={cn('item-value')}>{item.question}</div>
        </div>
      ))
    } else if (key === 'clue') {
      return data.hypothesis.clue.map((item, index) => (
        <div className={cn('detail-item')}>
          <div className={cn('item-label')}>{`线索${index + 1}`}</div>
          <div className={cn('item-value')}>
            <span>{item.des}</span>
          </div>
        </div>
      ))
    } else if (key === 'experiment') {
      return data.experiment.map((item, index) => (
        <div className={cn('detail-item')}>
          <div className={cn('item-label')}>{`实验${index + 1}`}</div>
          <div className={cn('item-value')}>{item.phenomenon}</div>
        </div>
      ))
    } else if (key === 'presume') {
      return data.hypothesis.presumes.map((item, index) => (
        <div className={cn('detail-item')}>
          <div className={cn('item-label')}>{`假设的${index + 1}.0版本`}</div>
          <div className={cn('item-value')}>
            地球内部存在三层结构，其特点如下：
            <br />
            {`最外层：${item.out.value}`}
            <br />
            {`中间层：${item.middle.value}`}
            <br />
            {`最内层：${item.in.value}`}
          </div>
        </div>
      ))
    } else if (key === 'conclusion') {
      return (
        <div className={cn('detail-item')}>
          <div className={cn('item-value', 'conclusion')}>{data.conclusion.conclusion_value}</div>
        </div>
      )
    }
  }

  return (
    <div className={cn('reports')}>
      <span className={cn('reports-title')}>
        反思报告
        <img className={cn('reports-key')} src={reflectionReport} />
      </span>
      <Popover
        placement="leftBottom"
        title={false}
        overlayClassName={cn('pop_overlay')}
        content={(
          <div className={cn('pop_bg')}>
            {
              CHECK_LIST.map((item) => (
                <div className={cn('check-item')} key={item}>
                  <img src={checkIconPng} />
                  {item}
                </div>
              ))
            }
          </div>
        )}
        trigger="click"
      >
        <img className={cn('checklist')} src={checkIcon} />
      </Popover>
      <div className={cn('content')}>
        {
          countList.map((item, index) => (
            <div className={cn('item', `${item.key}`, { active: active === item.key })} onClick={() => { setActive(item.key) }}>
              <span>{item.label}</span>
              {active !== item.key && <img className={cn('check-icon')} src={index % 2 === 0 ? defaultLeftPng : defaultRightPng} />}
              {active === item.key && <img className={cn('check-icon')} src={index % 2 === 0 ? activeLeftPng : activeRightPng} />}
            </div>
          ))
        }
        <div className={cn('detail', `${activeDirection}`)}>
          {renderContent(active)}
        </div>
      </div>
    </div>
  )
}

export default Reports
