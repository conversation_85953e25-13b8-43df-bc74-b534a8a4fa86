html,
body,
#root {
  height: 100%;
  margin: 0;
  background: transparent;
  font-family: "Alibaba PuHuiTi";
}

iframe {
  background-color: transparent;
}

body {
  overflow-x: hidden;
  color: #314659;
  font-size: 14px;
  font-family: Alibaba PuHuiTi, -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC",
    "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif,
    "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
  line-height: 1.5;
}

a {
  color: #1890ff;
  text-decoration: none;
  background-color: transparent;
  outline: none;
  cursor: pointer;
}

::-webkit-scrollbar {
  width: 0px;
  height: 0px;
  position: absolute;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 12px;
}

.fish-tabs,
.fish-tabs-content,
.fish-tabs-tabpane {
  height: 100%;
}

// 1280x800 屏幕
@media (max-width: 1280px) and (max-height: 800px) {
  html,
  body {
    font-size: 16px;
  }
}

// 1024*640 屏幕
@media (max-width: 1024px) and (max-height: 640px) {
  html,
  body {
    font-size: 11px;
  }
}


// 854*466 屏幕
@media (max-width: 854px) and (max-height: 466px) {
  html,
  body {
    font-size: 8px;
  }
}


@font-face {
  font-family: "Alibaba PuHuiTi";
  src: url("../../../public/Alibaba_PuHuiTi_2.0_55_Regular_55_Regular.ttf") format("truetype");
  font-weight: 500;
  font-display: swap;
}
