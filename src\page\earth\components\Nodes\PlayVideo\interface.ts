/**
 * 视频播放节点基础配置
 */
export interface IPlayVideoNode {
  id: string;
  type: 'play_video';
  content: VideoContent;
  next: string;
  meta?: NodeMeta;
  need_tts?: boolean
  voice_type?: string; // 语音类型
}

/**
 * 视频内容配置
 */
interface VideoContent {
  url: string;
  posterUrl?: string;
  playback: PlaybackConfig;
  tracks?: SubtitleTrack[];
  advanceOn: AdvanceCondition;
  analyticsEvents?: number[];
}

/**
 * 播放器行为配置
 */
interface PlaybackConfig {
  autoPlay: boolean;
  muted: boolean;
  loop: boolean;
  showControls: boolean;
}

/**
 * 字幕轨道配置
 */
interface SubtitleTrack {
  src: string;
  kind: 'subtitles' | 'captions' | 'descriptions';
  srclang: string; // BCP 47语言标签
  label: string;
}

/**
 * 流程继续条件
 */
interface AdvanceCondition {
  condition: 'end' | 'time_elapsed' | 'manual';
  durationInSeconds?: number; // 仅condition="time_elapsed"时有效
}

/**
 * 节点元数据配置
 */
interface NodeMeta {
  transitionIn?: 'fadeIn' | 'slideIn' | string; // 可扩展其他动画类型
  background?: string; // CSS颜色值
}
