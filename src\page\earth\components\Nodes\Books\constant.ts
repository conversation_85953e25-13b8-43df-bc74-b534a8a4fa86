import question1 from '@/asset/musics/book/question1.wav'
import question2 from '@/asset/musics/book/question2.wav'
import question3 from '@/asset/musics/book/question3.wav'
import diverge1 from '@/asset/musics/book/diverge1.wav'
import diverge2 from '@/asset/musics/book/diverge2.wav'
import diverge3 from '@/asset/musics/book/diverge3.wav'
import presume1 from '@/asset/musics/book/presume1.wav'
import presume2 from '@/asset/musics/book/presume2.wav'
import presume3 from '@/asset/musics/book/presume3.wav'
import iterate1 from '@/asset/musics/book/iterate1.wav'
import iterate2 from '@/asset/musics/book/iterate2.wav'
import tab4 from '@/asset/musics/book/tab_4.wav'

export const LABEL_MAP = {
  out: '最外层',
  middle: '中间层',
  in: '最内层'
}

export const DEFAULT_HY_TAB = 'question_presumes'

export const WHEEL_TABS = [
  {
    type: 'Why',
    labels: [
      '为什么会有这种现象发生？',
      '为什么会存在这样的物质/结构？',
      '为什么这个过程在某些条件下更明显？',
    ]
  },
  {
    type: 'What',
    labels: [
      '这是什么现象/事物？',
      '它的主要特征是什么？',
      '它的组成部分或要素都有哪些？',
    ]
  },
  {
    type: 'When',
    labels: [
      '这种现象在什么情况下或时间段最为明显？',
      '它的出现或结束是否有特定的时间节点？',
    ]
  },
  {
    type: 'Where',
    labels: [
      '这种现象或过程主要发生在什么区域/地点？',
      '这种物质来自于哪里？',
    ]
  },
  {
    type: 'How',
    labels: [
      '这种现象是如何产生或形成的？',
      '如果存在多个因素，不同因素之间是如何相互影响的？'
    ]
  }
]

export const CHECK_LIST = [
  '问题提出是否清晰、具体？',
  '假设的提出基于哪些线索？',
  '假设的可检验性如何？',
  '在实验过程中，操作是否按一定逻辑进行？',
  '模拟未能成功时，你是如何调整的？',
  '数据解释逻辑是否清晰？',
  '在探究过程中有哪些新的知识或技能收获？',
  '是否能将本次探究经历中形成的经验、态度迁移到其他学习或生活情境中？',
]

export const BOOK_GUIDE_MAP = {
  question1: {
    music: question1,
    dialogue: '请点击查看收集到的线索。再根据线索，提出若干具体的问题吧！',
  },
  question2: {
    music: question2,
    dialogue: '好问题像探险的地图——没有它，我们就不知道研究该往哪里走啦！',
  },
  question3: {
    music: question3,
    dialogue: '试试问：“为什么会出现这种现象？”或“这种物质是从哪里来的？”',
  },
  diverge1: {
    music: diverge1,
    dialogue: '选一条线索拖入问题发散器，然后点击转盘。',
  },
  diverge2: {
    music: diverge2,
    dialogue: '你会获得一些问题模板，结合线索提出你的问题。',
  },
  diverge3: {
    music: diverge3,
    dialogue: '是时候展示你头脑风暴的威力了！',
  },
  presume1: {
    music: presume1,
    dialogue: '现在，把问题变成可验证的科学假设吧！比如：“因为地球内部温度极高，所以岩石融化成了岩浆”。',
  },
  presume2: {
    music: presume2,
    dialogue: '假设是实验的靶心——没有它，实验就不知要验证什么！',
  },
  presume3: {
    music: presume3,
    dialogue: '记得把支持某个假设的线索与这个假设关联起来哦~',
  },
  iterate1: {
    music: iterate1,
    dialogue: '现在，把问题变成可验证的科学假设吧！比如：“因为地球内部温度极高，所以岩石融化成了岩浆”。',
  },
  iterate2: {
    music: iterate2,
    dialogue: '假设是实验的靶心——没有它，实验就不知要验证什么！',
  },
  tab4: {
    music: tab4,
    dialogue: '请你回想一下，此次探究过程中做的好的、和不足的地方有哪些？',
  }
}
