import { createSlice } from '@reduxjs/toolkit'

import type { PayloadAction } from '@reduxjs/toolkit'
import type { ICallbackParameter } from 'cloud-office-util/help/use-login-user'

// Define a type for the slice state
interface IUserState {
  userName?: string
  accountId?: number | string
  userId?: number,
  gender?: number,
  accountType?: 'org' | 'guest' | 'person'
  isGuest?: boolean
}

// Define the initial state using that type
const initialState: IUserState = {
  isGuest: true,
}

export const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    update: (state, action: PayloadAction<ICallbackParameter | undefined>) => {
      if (action.payload === undefined) return

      const { accountType, accountInfo } = action.payload
      state.isGuest = accountType === 'guest'

      const {
        user_id, account_id, gender, name
      } = accountInfo
      state.userName = name
      state.userId = user_id
      state.accountId = account_id
      state.gender = gender
    },
  }
})

// Action creators are generated for each case reducer function
export const { update } = userSlice.actions

export default userSlice.reducer
