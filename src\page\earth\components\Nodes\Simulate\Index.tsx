import React, { useEffect, useState } from 'react'
import classNames from 'classnames/bind'

import roundPng from '@/asset/images/simulate/simulate_round.png'
import defaultPng from '@/asset/images/simulate/simulate_default.png'
import EventBus from '@/component/EventBus'
import { Events } from '@/component/EventBus/constant'
import { useAppSelector } from '@/store'
import styles from './index.module.scss'

const cn = classNames.bind(styles)

const Simulate = () => {
  const { data } = useAppSelector((state) => state.earth)
  const {
    currentGroupId, currentNodeId
  } = data || {}
  const [showSimulate, setShowSimulate] = useState(false)

  useEffect(() => {
    // 节点变了恢复默认状态
    setShowSimulate(false)
  }, [currentGroupId, currentNodeId])

  useEffect(() => {
    EventBus.on(Events.onSimulateSettingPanel, ({ isOn }) => {
      setShowSimulate(isOn)
    })
    return () => {
      EventBus.off(Events.onSimulateSettingPanel)
    }
  }, [])
  const onChangeSimulate = () => {
    setShowSimulate(!showSimulate)
    EventBus.emit(Events.sendMessage, 'SimulateSettingPanel', { isOn: !showSimulate })
  }

  return (
    <div className={cn('simulate')} onClick={onChangeSimulate}>
      {showSimulate && <img src={roundPng} className={cn('round')} />}
      <img src={defaultPng} className={cn('default')} />
    </div>
  )
}

export default Simulate
