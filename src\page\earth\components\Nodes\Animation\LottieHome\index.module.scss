.lottie-home {
  position: fixed;
  width: 100vw;
  height: 100vh;
  top: 0;
  left: 99999rem;
  z-index: 99999;
 
  &.play {
    left: 0rem;
  }
  .lottie-container {
    width: 100%;
    height: 100%;
    transform: translate3d(0, 0, 0); /* 强制GPU加速 */
    will-change: transform;
    isolation: isolate; /* 创建新图层 */
    backface-visibility: hidden;
    animation: memoryLock 1s infinite; /* 持续占用信号 */
  }
}

@keyframes memoryLock {
  0% {
    opacity: 0.999;
  }
  100% {
    opacity: 1;
  }
}
