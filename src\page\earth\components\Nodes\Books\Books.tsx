import React, { FC, useState } from 'react'
import classNames from 'classnames/bind'
import { useAppDispatch, useAppSelector } from '@/store'
import styles from './index.module.scss'
import { updateData } from '../../../earthSlice'
import Hypothesis from './BookTab/Hypothesis/Hypothesis'
import Experiment from './BookTab/Experiment/Experiment'
import Conclusion from './BookTab/Conclusion/Conclusion'
import Reports from './BookTab/Reports/Reports'

const cn = classNames.bind(styles)

export const BOOK_TABS = [
  {
    key: 'hypothesis', label: '提出假设'
  },
  {
    key: 'experiment', label: '调查实验'
  },
  {
    key: 'conclusion', label: '实验结论'
  },
  {
    key: 'reports', label: '反思报告'
  }
]

interface IBooksProps {
  defaultTab: string
}
const Books: FC<IBooksProps> = ({ defaultTab }) => {
  const { data } = useAppSelector((state) => state.earth)
  const { books } = data
  const [activeTab, setActiveTab] = useState<string>(defaultTab || 'hypothesis')

  const dispatch = useAppDispatch()

  const onChangeData = (newData) => {
    dispatch(updateData(newData))
  }

  const renderTabPane = () => {
    switch (activeTab) {
      case 'hypothesis':
        return (
          <Hypothesis
            data={books.hypothesis}
            onChange={(_data) => {
              onChangeData({
                books: {
                  ...books,
                  hypothesis: {
                    ...(books.hypothesis || {}),
                    ...(_data || {})
                  }
                }
              })
            }}
          />
        )
      case 'experiment':
        return (
          <Experiment
            data={books.experiment}
            onChange={(_data) => {
              onChangeData({
                books: {
                  ...books,
                  experiment: _data
                }
              })
            }}
          />
        )
      case 'conclusion':
        return (
          <Conclusion
            data={books}
            onChange={(_data) => {
              onChangeData({
                books: {
                  ...books,
                  ..._data
                }
              })
            }}
          />
        )
      case 'reports':
        return (
          <Reports
            data={books}
          />
        )
      default:
        return null
    }
  }

  return (
    <div className={cn('books')}>
      <div className={cn('books-content', `${activeTab}`)}>
        {renderTabPane()}
      </div>
      <div className={cn('books-tabs')}>
        {BOOK_TABS.map((item, index) => (
          <div
            key={item.key}
            className={cn('books-tab-item', { active: item.key === activeTab })}
            onClick={() => {
              setActiveTab(item.key)
            }}
            style={{ top: `-${index}rem`, zIndex: `${5 - index}` }}
          >
            <span>{item.label}</span>
          </div>
        ))}
      </div>
    </div>
  )
}

export default Books
