{"name": "aic-earth-research", "version": "1.0.0", "scripts": {"start": "gms dev", "build": "gms build", "build:modern": "gms build --modern", "cache:clean": "rimraf node_modules/.cache", "lint": "gms lint", "lint:fix": "gms lint --fix", "test:unit": "gms test:unit", "start:test": "cross-env SDP_ENV=test npm run start", "start:pre": "cross-env SDP_ENV=preproduction npm run start", "start:pro": "cross-env SDP_ENV=product npm run start", "start:sdpsg": "cross-env SDP_ENV=sdpsg npm run start", "cap:sync": "npx cap sync", "android": "node scripts/android-build.js", "android:release": "node scripts/android-build.js --release", "android:clean": "cd android && .\\gradlew clean"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{css,less,sass,scss}": ["stylelint --fix", "git add"], "src/**/*.{js,jsx,ts,tsx}": ["eslint --fix", "git add"]}, "dependencies": {"@capacitor/android": "^3.4.0", "@capacitor/cli": "^3.4.0", "@capacitor/core": "^3.4.0", "@gem-mine/immutable": "^1.0.0", "@gem-mine/intl": "^2.0.5", "@gem-mine/request": "^2.0.2", "@reduxjs/toolkit": "^1.7.0", "@sdp.nd/ai-courseware-component": "0.2.4", "@sdp.nd/cloud-office-util": "^0.10.7", "@sdp.nd/fish": "^3.12.3", "@sdp.nd/js-cs-sdk": "^2.2.23", "@sdp.nd/one": "^3.3.4", "@sdp.nd/uc-selector": "^1.3.15", "@x-edu/tracker": "^2.1.0", "@xzdarcy/react-timeline-editor": "^0.1.9", "ahooks": "^3.7.8", "audiobuffer-to-blob": "^1.0.2", "byted-ailab-speech-sdk": "^4.0.9", "camera-controls": "^2.7.2", "classnames": "^2.3.1", "cnchar": "^3.2.6", "cnchar-order": "^3.2.6", "copy-to-clipboard": "^3.3.3", "core-js": "^3.0.0", "dexie": "^4.0.11", "escape-html": "^1.0.3", "eventemitter3": "^5.0.1", "fabric": "^5.2.1", "file-saver": "^2.0.5", "gif.js": "^0.2.0", "howler": "^2.2.3", "html2canvas": "^1.4.1", "immer": "^10.0.2", "js-audio-recorder": "^1.0.7", "jszip": "^3.10.1", "lamejs": "1.2.0", "lodash": "^4.17.21", "lottie-react": "^2.4.0", "lottie-web": "^5.12.2", "memfs": "^4.17.0", "moment": "^2.29.1", "prop-types": "^15.7.2", "react": "^16.7.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dnd-multi-backend": "^6.0.2", "react-dnd-touch-backend": "^16.0.1", "react-document-title": "^2.0.3", "react-dom": "^16.7.0", "react-draggable": "^4.5.0", "react-lines-ellipsis": "^0.15.4", "react-loadable": "^5.5.0", "react-redux": "^7.2.6", "react-router-dom": "^6.2.2", "react-sortable-hoc": "^2.0.0", "react-split-pane": "^0.1.92", "react-virtualized": "^9.22.6", "recordrtc": "^5.6.2", "redux": "^4.1.2", "redux-undo": "^1.1.0", "setprototypeof": "^1.2.0", "three": "^0.155.0", "typeit": "^8.8.7", "urijs": "^1.19.7", "use-immer": "^0.9.0", "uuid": "^8.3.2", "video.js": "^7.21.5", "xlsx": "^0.18.5"}, "devDependencies": {"@gem-mine/babel-preset-app": "^3.2.11", "@gem-mine/cli-plugin-eslint": "^4.1.3", "@gem-mine/cli-plugin-jest": "^3.0.2", "@gem-mine/cli-plugin-typescript": "^4.0.4", "@gem-mine/eslint-config-recommend-ts": "^5.0.2", "@gem-mine/script": "^3.3.0", "@types/fabric": "^5.3.7", "@types/file-saver": "^2.0.7", "@types/jest": "^26.0.19", "@types/node": "14.0.17", "@types/prop-types": "^15.7.1", "@types/react": "^16.7.0", "@types/react-document-title": "^2.0.5", "@types/react-dom": "^16.7.0", "@types/react-loadable": "^5.5.0", "@types/three": "^0.155.1", "@types/urijs": "^1.19.18", "@types/uuid": "^8.3.4", "@types/webpack-env": "1.15.3", "babel-plugin-import": "^1.11.0", "babel-plugin-lodash": "^3.3.4", "cross-env": "^7.0.3", "enzyme": "^3.11.0", "enzyme-adapter-react-16": "^1.15.2", "eslint": "^7.5.0", "husky": "^2.7.0", "jest": "^27.0.6", "jest-enzyme": "^7.1.1", "lint-staged": "^9.5.0", "mockjs": "^1.0.1-beta3", "moment-locales-webpack-plugin": "^1.2.0", "react-test-renderer": "^16.12.0", "rimraf": "^3.0.0", "sass": "^1.32.8", "sass-loader": "^8.0.2", "stylelint": "^9.10.1", "stylelint-config-recommended": "^2.1.0", "stylelint-config-standard": "^18.2.0", "ts-jest": "^27.0.4", "typescript": "^4.6.1", "webpack": "^5.45.1"}, "volta": {"node": "14.21.3"}}