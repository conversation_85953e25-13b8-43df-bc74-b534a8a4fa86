import { <PERSON><PERSON>ey } from './components/Nodes/Achievement/constant'
import { IIframeUrlNode } from './components/Nodes/IframeUrl/interface'
import { IOpenComponentNode } from './components/Nodes/OpenComponent/interface'
import { IOpenUrlNode } from './components/Nodes/OpenUrl/interface'
import { IPlayVideoNode } from './components/Nodes/PlayVideo/interface'

/*
节点配置文档定义说明
*/
// https://jqy8ifua9c.feishu.cn/docx/LJI4d6dwHonutXxk9P9cyAfNnzg
export type IEarthNode = (IPlayVideoNode | IOpenUrlNode | IIframeUrlNode | IOpenComponentNode) & {
  bgm?: string // 节点背景音乐
  res: string[]
}

export interface IEarthGroup {
  id: string // 节点组的唯一标识符，用于跳转和状态管理
  nodes: IEarthNode[] //  节点列表，一次播放
  name: string,
  count_score: boolean // 计算积分
  count_medal: boolean // 计算奖章
  next: string // 默认的下一个节点ID（线性流程）
}

export interface IEarthConfigInfo {
  id?: string
  name?: string
  index_page: {
    url: string,
    audio_url: string,
    pre_download_res: boolean
  },
  node_groups: IEarthGroup[] // 节点组列表
}

interface IClue {
  id: string
  img: string
  title: string
  des: string
}

interface IQuestionPresumes {
  id: string
  question: string // 问题
  presume: string // 假设
  relation_clue: string[] // 线索id
}

interface IPresumes {
  id: string
  out: {
    value: string
    relation_clue: string[]
  } // 最外层
  middle: {
    value: string
    relation_clue: string[]
  } // 中间层
  in: {
    value: string
    relation_clue: string[]
  }// 最内层
}

interface IExperiment {
  id: string
  question_why: string
  question_where: string
  phenomenon: string
  success: boolean,
  params: {
    level: string
    material: string
    thickness: string
    move?: string
    gap?: boolean
  }[]
}

export enum UPGRADE_KEY {
  volcano = 'volcano', // 火山
  mountain = 'mountain', // 高山
  earthquake = 'earthquake', // 地震
  question = 'question', // 提问
  hypothesis = 'hypothesis', // 假设
  iterate_hypothesis = 'iterate_hypothesis', // 迭代假设
  experiment = 'experiment', // 实验
  conclusion = 'conclusion' // 结论
}

interface IMessage {
  id: string
  content: string
  role: 'user' | 'assistant'
  timestamp: number
  sessionId: string
  meta?: {
    liked?: boolean
    audio?: string
  }
}

interface IAiData {
  sessionId: string
  messages: IMessage[]
}

export interface IEarthData {
  prevNode: any // 上个节点
  manualBook: boolean // 是否手动打开笔记本
  currentGroupId: string // 当前组ID
  currentNodeId: string // 当前节点ID
  questionUnLock: boolean // 问题锁，第一次自动笔记本不显示假设
  iterateUnLock: boolean // 迭代锁
  simulateUnLock: boolean // 模拟锁
  guided: {
    [x: string]: boolean // 记录节点对应是否引导过。 home | next
  }
  config: {
    aiLevel: 'high' | 'middle' | 'low' // ai辅助等级设置
    achievement: boolean // 成就系统开启
    aiMode: boolean // AI模式是否开启
  }
  books: {
    hypothesis: {
      clue: IClue[] // 线索数据
      question_presumes: IQuestionPresumes[]
      presumes: IPresumes[]
    }
    experiment: IExperiment[]
    conclusion: {
      conclusion_value: string
    }
    reports: any
  }
  // 成就系统，积分和勋章相关
  achievement: {
    medals: {
      time: number // 获取勋章的时间戳
      key: MedalKey // 勋章ID
      unlocked: boolean // 是否解锁
      upgrade?: { [x in UPGRADE_KEY]: boolean } // 记录升级勋章达成状态
    }[] // 勋章列表
    [x: string]: any
  }
  // 带历史数据的ai对话，支持上下文记忆，记录是否主动唤起ai
  ai: {
    messages: IAiData[]
    count: number // 记录唤起次数
    activeCount: number // 主动唤起次数
    [x: string]: any
  }
  simulate: {
    errorCount: number
    [x: string]: any
  }
}
