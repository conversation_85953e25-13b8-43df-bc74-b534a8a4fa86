import classNames from 'classnames/bind'
import React from 'react'
import DocumentTitle from 'react-document-title'
import { Outlet } from 'react-router-dom'
import styles from './index.module.scss'

const cn = classNames.bind(styles)

const TITLE = '地球侦探笔记'
const Home = () => (
  <DocumentTitle title={TITLE}>
    <div className={cn('home')}>
      <Outlet />
    </div>
  </DocumentTitle>
)

export default Home
