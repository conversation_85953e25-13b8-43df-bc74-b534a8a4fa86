import 'package:dio/dio.dart';
import 'package:grey_publish/grey_publish.dart';
import 'package:ppt101_uc_utils/uc_utils.dart';

const String imUrl = "http://im-agent.web.sdp.101.com";

// 发给群 dart .\send_99u.dart {群ID} "消息内容\n消息内容" true
// 发给个人 dart .\send_99u.dart {用户ID} "消息内容\n消息内容"
void main(List<String> args) async {
  print("main cmd: $args");
  List<String> ids = args[0].split(",");
  String message = args[1];
  bool isGroup = args.length == 3 ? args[2] == "true" : false;

  UcTokenModel? token =
      await login("10009707", "94b1706d8355d5b08e7f225b965640d9");
  print(token.toString());
  if (token == null) {
    print("token is null");
    return;
  }

  send99uMessage(token, ids, message, isGroup);
}

Future<bool> send99uMessage(
  UcTokenModel token,
  List<String> ids,
  String message,
  bool isGroup,
) async {
  String path = "/v0.2/api/agents/messages";
  Uri uri = Uri.parse(imUrl + path);
  message = message.replaceAll("\\n", "\r\n");

  Map json = {
    "filter": [
      {
        "name": isGroup ? "gid" : "uri", //筛选条件名称，固定。
        "args": {isGroup ? "gid" : "uri_list": ids} //用户UID或主体标识，最大50个
      }
    ],
    "body": {
      "content": 'Content-Type:text/plain\r\n\r\n$message',
      "content_summary": message,
    }
  };

  Map<String, dynamic> headers = {
    "Authorization": context.generateAuthorization(
        method: "POST", host: uri.host, path: uri.path, token: token)
  };
  Dio dio = Dio();
  Response response = await dio.post(uri.toString(),
      data: json,
      options: Options(headers: headers, validateStatus: (_) => true));
  print(response);
  return false;
}
