import React, { FC, useMemo } from 'react'
import {
  useDrag, useDrop, useDragLayer
} from 'react-dnd'

import offPng from '@/asset/images/books/hypothesis/target_imgs/clue_paste_off.png'
import onPng from '@/asset/images/books/hypothesis/target_imgs/clue_paste_on.png'
import hoverPng from '@/asset/images/books/hypothesis/target_imgs/clue_paste_hover.png'
import wheelOnPng from '@/asset/images/books/hypothesis/wheel/clue_default.png'
import wheelHoverPng from '@/asset/images/books/hypothesis/wheel/clue_hover.png'

export const CustomDragPreview: FC<any> = () => {
  const {
    isDragging, item, currentOffset
  } = useDragLayer((monitor) => ({
    item: monitor.getItem(),
    itemType: monitor.getItemType(),
    currentOffset: monitor.getSourceClientOffset(),
    isDragging: monitor.isDragging()
  }))
  if (!isDragging || !currentOffset || !item) return null

  const { x, y } = currentOffset

  // 默认宽度为 180px，计算居中偏移
  const imageWidth = document.querySelector(`[data-id=${item.id}]`)?.clientWidth || 180
  const imageHeight = imageWidth * 0.8 // 假设图片高度比例

  const previewStyles = (_x: number, _y: number): React.CSSProperties => ({
    position: 'fixed',
    pointerEvents: 'none',
    zIndex: 9999,
    left: 0,
    top: 0,
    // 使图片中心点跟随鼠标位置，并添加小幅度偏移避免遮挡鼠标
    transform: `translate(${(_x - imageWidth / 2)}px, ${(_y - imageHeight / 2)}px)`,
    opacity: 0.8,
    transition: 'none', // 确保没有过渡动画影响拖拽流畅度
  })

  return (
    <div style={previewStyles(x, y)}>
      <img
        src={item.img}
        style={{
          width: imageWidth,
          height: 'auto',
          borderRadius: '0.25rem',
          boxShadow: '0 0.25rem 0.75rem rgba(0, 0, 0, 0.15)'
        }}
        alt="drag preview"
      />
    </div>
  )
}

export const TargetBox: FC<any> = ({
  onDrop, can = false, children,
  hasRc, wheel = false
}) => {
  const [{ isOver }, drop] = useDrop(
    () => ({
      accept: ['img'],
      drop(_item, monitor) {
        onDrop(monitor.getItem())
        return undefined
      },
      // canDrop: () => can,
      collect: (monitor) => ({
        isOver: monitor.canDrop() && monitor.isOver(),
        canDrop: monitor.canDrop(),
      }),
    }),
    [onDrop],
  )

  console.log('isOver', isOver)
  const containerStyle = useMemo(
    () => {
      let background = `url(${offPng})`
      if (can) {
        background = isOver ? `url(${hoverPng})` : `url(${onPng})`
        if (wheel) {
          background = isOver ? `url(${wheelHoverPng})` : `url(${wheelOnPng})`
        }
      }

      if (hasRc) {
        background = 'unset'
      }
      return {
        width: '100%',
        height: '100%',
        backgroundImage: background,
        backgroundSize: 'contain',
        backgroundRepeat: 'no-repeat',
        backgroundPosition: 'center',
      }
    },
    [isOver, can],
  )

  return (
    <div
      ref={drop}
      role="TargetBox"
      style={{
        ...containerStyle,
      }}
    >
      {children}
    </div>
  )
}

export const SourceBox: FC<any> = ({
  onDrag, onEnd, id, img, children
}) => {
  const [{ isDragging }, drag] = useDrag(
    () => ({
      type: 'img',
      item: () => {
        if (onDrag) {
          onDrag(id)
        }
        return { id, img }
      },
      end: () => {
        if (onEnd) {
          onEnd()
        }
      },
      canDrag: true,
      collect: (monitor) => ({
        isDragging: monitor.isDragging(),
      }),
    }),
    [id, onDrag, onEnd],
  )

  const containerStyle = useMemo(
    () => ({
      display: 'inline-block',
      height: 'auto',
      opacity: isDragging ? 0 : 1,
      cursor: 'move',
    }),
    [isDragging],
  )

  return (
    <span ref={drag} style={containerStyle} role="SourceBox" data-id={id}>
      {children}
    </span>
  )
}
