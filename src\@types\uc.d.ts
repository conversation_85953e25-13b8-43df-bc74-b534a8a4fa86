declare namespace NsUc {
  export interface UserInfo {
    nick_name: string
    real_name: string
    org_user_code: string
    org_code: string
    node_items?: Array<{ node_name: string }>
    user_id: number
  }

  export interface OrgInfo {
    org_code: string
    org_id: number
    org_name: string
  }

  export interface NodeInfo {
    node_id: number
    node_name: string
    node_path: string
    node_type: string
    org_id: number
    parent_id: number
  }
}
