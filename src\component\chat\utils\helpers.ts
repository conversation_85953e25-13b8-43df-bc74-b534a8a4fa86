/**
 * 工具函数
 */
// eslint-disable-next-line import/prefer-default-export
export const utils = {
  /**
   * 生成UUID
   */
  generateId: (): string => `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,

  /**
   * 格式化时间
   */
  formatTime: (timestamp: number, currentTime?: number): string => {
    const date = new Date(timestamp)
    const now = new Date(currentTime || Date.now())
    const diff = now.getTime() - date.getTime()

    if (diff < 60000) { // 1分钟内
      return '刚刚'
    } else if (diff < 3600000) { // 1小时内
      return `${Math.floor(diff / 60000)}分钟前`
    } else if (diff < 86400000) { // 24小时内
      return `${Math.floor(diff / 3600000)}小时前`
    } else {
      return date.toLocaleDateString()
    }
  },

  /**
   * 防抖函数
   */
  debounce: <T extends (...args: any[]) => any>(
    func: T,
    wait: number
  ): ((...args: Parameters<T>) => void) => {
    let timeout: number | null = null
    return (...args: Parameters<T>): void => {
      if (timeout !== null) {
        clearTimeout(timeout)
      }
      timeout = window.setTimeout(() => func(...args), wait)
    }
  },

  /**
   * 节流函数
   */
  throttle: <T extends (...args: any[]) => any>(
    func: T,
    wait: number
  ): ((...args: Parameters<T>) => void) => {
    let inThrottle = false
    return (...args: Parameters<T>): void => {
      if (!inThrottle) {
        func(...args)
        inThrottle = true
        setTimeout(() => { inThrottle = false }, wait)
      }
    }
  },

  /**
   * 检查是否为移动设备
   */
  isMobile: (): boolean => /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
    navigator.userAgent
  )
}
