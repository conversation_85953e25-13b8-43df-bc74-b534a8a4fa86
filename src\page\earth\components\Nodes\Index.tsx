import React, {
  useEffect, useMemo, useRef, useState
} from 'react'
import classNames from 'classnames/bind'
import { useAppDispatch, useAppSelector } from '@/store'
import EventBus from '@/component/EventBus'
import { Events } from '@/component/EventBus/constant'
import { useClickAway, useLatest } from 'ahooks'
import AudioManager from '@/util/AudioManager'
import { getElementPosition } from '@/util/box'
import styles from './index.module.scss'
import { updateData } from '../../earthSlice'
import Achievement from './Achievement/Index'
import { IEarthNode } from '../../interface'
import PlayVideo from './PlayVideo/Index'
import IframeUrl from './IframeUrl/Index'
import BooksButton from './Books/BooksButton'
import HomeGuide from '../HomeGuide/Index'
import SetConfig from '../SetConfig/Index'
import { MedalKey } from './Achievement/constant'
import { useUnLockMedals } from '../../hooks/useAchievement'
import AiChat from './AiChat'
import Simulate from './Simulate/Index'
import FingerTip from './Animation/FingerTip'
import DialogueGuide from '../HomeGuide/DialogueGuide'
import LottieHome from './Animation/LottieHome/Index'
import { DataTracker } from '@/sdk/Tracker'

const cn = classNames.bind(styles)

const Nodes = () => {
  const { data, nodeInfo } = useAppSelector((state) => state.earth)
  const {
    currentGroupId, currentNodeId, guided,
    prevNode, manualBook, questionUnLock, iterateUnLock, simulateUnLock
  } = data || {}
  const dispatch = useAppDispatch()
  const { unlockMedal } = useUnLockMedals()
  const nodeListRef = useRef<HTMLDivElement>(null)
  const [expand, setExpand] = useState(false)
  const guidedRef = useRef<any>(null)
  const nextStepRef = useRef<any>(null)
  const lastQuestionUnLock = useLatest(questionUnLock)
  const lastIterateUnLock = useLatest(iterateUnLock)
  const lastSimulateUnLock = useLatest(simulateUnLock)

  useClickAway(() => {
    if (expand) {
      setExpand(false)
    }
  }, nodeListRef)

  const onChangeData = (newData) => {
    dispatch(updateData(newData))
  }

  const currentGroup = useMemo(() => nodeInfo.node_groups.find((item) => item.id === currentGroupId), [nodeInfo, currentGroupId])

  const currentNode = useMemo(() => {
    if (currentGroup && currentNodeId) {
      return currentGroup?.nodes?.find((item) => item.id === currentNodeId) || {} as unknown as IEarthNode
    }
    return {} as unknown as IEarthNode
  }, [currentGroup, currentNodeId])

  useEffect(() => {
    // 如果没当前分组就初始化当前分组
    if (!data?.currentGroupId && nodeInfo) {
      dispatch(updateData({
        currentGroupId: nodeInfo.node_groups?.[0].id,
        currentNodeId: nodeInfo.node_groups?.[0]?.nodes?.[0].id,
        prevNode: undefined
      }))
    } else if (data?.currentGroupId && !data?.currentNodeId && nodeInfo) {
      // 如果有当前分组但是没当前节点则初始化节点
      dispatch(updateData({
        currentNodeId: currentGroup?.nodes?.[0].id, prevNode: undefined
      }))
    }
  }, [data, nodeInfo])

  const onNext = () => {
    onChangeData({ currentGroupId: currentGroup?.next, currentNodeId: '', prevNode: undefined })
    if (currentNode?.type === 'open_component') {
      EventBus.emit(Events.CloseComponent, currentNode.content.component_name)
    }
    DataTracker.getInstance().track({
      eventName: 'node_next',
      params: {
        currentGroupId: currentGroup?.id,
        currentNodeId,
        prevNodeId: prevNode?.id,
      },
    })
  }

  const onNodeEnd = () => {
    const index = currentGroup!.nodes.findIndex((item) => item.id === currentNodeId)!
    if (index === currentGroup!.nodes!.length - 1) {
      if (!guided.next) {
        const position = getElementPosition(nextStepRef.current!)
        EventBus.emit(Events.fingerTip, {
          text: '点击下一步',
          direction: 'right',
          position: { x: position.x - 40, y: position.y },
          guideKey: 'next'
        })
      }
      // dispatch(updateData({ currentGroupId: currentGroup?.next, currentNodeId: '', prevNode: undefined }))
    } else {
      dispatch(updateData({ currentNodeId: currentGroup?.nodes?.[index + 1].id, prevNode: currentNode }))
    }
  }

  const renderNode = () => {
    if (currentNode?.type === 'play_video' || (currentNode?.type === 'open_component' && prevNode?.type === 'play_video')) {
      const info = currentNode?.type === 'play_video' ? currentNode : prevNode
      const key = currentNode?.type === 'play_video' ? currentNode.id : prevNode.id
      return <PlayVideo guided={guided.home} info={info} onEnd={onNodeEnd} key={key} />
    } else if (currentNode?.type === 'iframe_url' || (currentNode?.type === 'open_component' && prevNode?.type === 'iframe_url')) {
      const info = currentNode?.type === 'iframe_url' ? currentNode : prevNode
      const key = currentNode?.type === 'iframe_url' ? currentNode.id : prevNode.id
      return <IframeUrl info={info} onNext={onNodeEnd} key={key} id={key} />
    }
  }

  useEffect(() => {
    if (currentNode?.type === 'open_component') {
      if (!lastQuestionUnLock.current && currentNode.id === 'node_9') {
        dispatch(updateData({
          questionUnLock: true
        }))
      } else if (!lastIterateUnLock.current && currentNode.id === 'node_13') {
        dispatch(updateData({
          questionUnLock: true,
          iterateUnLock: true
        }))
      }
      EventBus.emit(Events.OpenComponent, {
        name: currentNode.content.component_name,
        tab: currentNode.content.tab,
        id: currentNode.id,
      })
    }
    if (!lastSimulateUnLock.current && currentNode.id === 'node_15') {
      dispatch(updateData({
        questionUnLock: true,
        iterateUnLock: true,
        simulateUnLock: true
      }))
    }
  }, [currentNode?.type])

  useEffect(() => {
    if (currentNode?.bgm) {
      AudioManager.getInstance('bgm').play(currentNode.bgm, true)
    }
    return () => {
      AudioManager.getInstance('bgm').stop()
    }
  }, [currentNode?.bgm])

  useEffect(() => {
    // 地球研究员升级额外处理
    unlockMedal(MedalKey.curious_rocket)
    unlockMedal(MedalKey.explorer)
    unlockMedal(MedalKey.deep_explorer)
    unlockMedal(MedalKey.perseverance)
    unlockMedal(MedalKey.communication_pioneer)
  }, [data])

  const isGuided = !guided.home // isGuided表示当前在引导中

  return (
    <div className={cn('nodes')}>
      {isGuided && <SetConfig guided onGuided={() => { guidedRef.current?.onDone() }} />}
      {isGuided && (
        <div className={cn('header', 'guide_header')} onClick={() => { guidedRef.current?.onDone() }}>
          <div className={cn('node-title')}>
            <span className={cn('node-title-text')}>{currentGroup?.name}</span>
            <span className={cn('node-title-icon', { expand })} />
          </div>
        </div>
      )}
      <div className={cn('header')}>
        {!isGuided && <SetConfig guided={false} onGuided={() => { guidedRef.current?.onDone() }} />}
        <div
          className={cn('node-title')}
          onClick={() => {
            setTimeout(() => {
              setExpand(!expand)
            }, 0)
          }}
        >
          <span className={cn('node-title-text')}>{currentGroup?.name}</span>
          <span className={cn('node-title-icon', { expand })} />
        </div>
        <div className={cn('node-list', { expand })} ref={nodeListRef}>
          <div className={cn('node-list-header')}>实验步骤</div>
          <div className={cn('node-list-content')}>
            {
              (nodeInfo.node_groups || []).map((group) => (
                <div
                  className={cn('node-list-item', { active: currentGroupId === group.id })}
                  key={group.id}
                  onClick={() => {
                    onChangeData({
                      currentGroupId: group.id,
                      currentNodeId: ''
                    })
                    setExpand(false)
                  }}
                >
                  {group.name}
                </div>
              ))
            }
          </div>
        </div>
        <div className={cn('right')}>
          {
            data?.config?.achievement && <Achievement />
          }
        </div>
      </div>
      <div ref={nextStepRef} className={cn('next', { top: manualBook === false })} onClick={onNext}>
        下一步
      </div>
      <BooksButton guided={isGuided} onGuided={() => { guidedRef.current?.onDone() }} />
      {lastSimulateUnLock.current && <Simulate />}
      <div className={cn('content')}>
        {
          renderNode()
        }
      </div>
      {isGuided && <HomeGuide ref={guidedRef} />}
      <DialogueGuide />
      {!isGuided && <AiChat />}
      <FingerTip />
      <LottieHome />
    </div>
  )
}

export default Nodes
