.floatingWidget {
  position: fixed;
  z-index: 9999;

  .avatar {
    width: 7.5rem;
    height: 7.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    cursor: pointer;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;

    .avatarImg {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .defaultAvatar {
      font-size: 1.125rem;
      font-weight: bold;
    }
  }
}

// 拖拽时的样式
.floatingWidget.react-draggable-dragging {
  .avatar {
    cursor: grabbing !important;
    cursor: -webkit-grabbing !important;
    transform: scale(1.05);
    transition: none;
  }
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

.messageContent {
  padding: 0.875rem 1.125rem 0.25rem 1.125rem;
  position: absolute;
  display: inline-block;
  transition: all 0.2s ease;
  font-size: 1rem;
  color: #2d2c40cc;
  min-width: fit-content; // 允许根据内容调整宽度
  width: auto; // 自动宽度
  border-radius: 1.25rem;
  border: 0.25rem solid rgba(255, 255, 255, 0.7);
  background: linear-gradient(
      102deg,
      rgba(255, 255, 255, 0) 41.11%,
      rgba(150, 98, 255, 0.2) 113.05%
    ),
    linear-gradient(180deg, #b8eaff 0%, #cee9ff 100%);
  box-shadow: 0 -0.0625rem 0.25rem 0 #b3fff7 inset;
  left: 6.875rem;
  top: 3.125rem;
  .arrowIcon {
    position: absolute;
    top: 0;
    left: -1.125rem;
    z-index: 1;
  }
  .bgIcon {
    position: absolute;
    bottom: 0;
    right: 0;
    max-width: 4.375rem;
    width: 100%;
  }
  .messageText {
    word-break: break-word;
    margin: 0;
    min-height: 1.6em; // 至少一行的高度
    max-height: 4.8em; // 最多显示3行 (1.6em * 3)
    overflow-y: auto; // 垂直滚动

    // 处理长文本
    overflow-wrap: break-word;
    hyphens: auto;

    // 确保内容对齐
    display: inline-block;
    width: max-content; // 根据内容自动调整宽度
    min-width: 0; // 允许收缩
    max-width: calc(50vw - 6.25rem);
  }

  &.right {
    background: linear-gradient(
        258deg,
        rgba(255, 255, 255, 0) 41.11%,
        rgba(150, 98, 255, 0.2) 113.05%
      ),
      linear-gradient(180deg, #b8eaff 0%, #cee9ff 100%);
    left: unset;
    right: 7.5rem;

    .bgIcon {
      left: 0;
      right: unset;
      transform: rotateY(180deg);
    }
    .arrowIcon {
      left: unset;
      right: -1.125rem;
      z-index: 1;
      rotate: 180deg;
    }
  }
}
