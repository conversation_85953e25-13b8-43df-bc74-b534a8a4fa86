export enum SendEventName {
  PageReady = 'PageReady',
  ActivityData = 'ActivityData',
  PageEnd = 'PageEnd',
}

export enum ListenEventName {
  CommonData = 'CommonData',
  ActivityData = 'ActivityData',
}
class PostMessageSDK {
  private static instance: PostMessageSDK

  private targetOrigin: string

  private constructor(targetOrigin: string) {
    if (!targetOrigin) {
      throw new Error('targetOrigin is required for security reasons.')
    }
    this.targetOrigin = targetOrigin
  }

  /**
   * 获取单例实例
   * @param targetOrigin 父页面的目标 origin
   */
  public static getInstance(targetOrigin: string): PostMessageSDK {
    if (!PostMessageSDK.instance) {
      PostMessageSDK.instance = new PostMessageSDK(targetOrigin)
    }
    return PostMessageSDK.instance
  }

  /**
   * 发送消息到父页面
   * @param type 消息类型
   * @param payload 消息内容
   */
  public sendMessage(message: { eventName: string, data: any }): void {
    if (!window.parent) {
      console.warn('No parent window found.')
      return
    }
    console.log('send message:', message, PostMessageSDK.instance.targetOrigin)
    window.parent.postMessage(message, PostMessageSDK.instance.targetOrigin)
  }

  /**
   * 监听来自父页面的消息
   * @param callback 回调函数
   */
  public onMessage(callback: (eventName: string, data: any) => void): () => void {
    const listener = (event: MessageEvent) => {
      const { eventName, data } = event.data
      if (eventName) {
        console.log('received message:', event)
        callback(eventName, data)
      }
    }

    window.addEventListener('message', listener)

    // 返回一个函数用于移除监听器
    return () => {
      window.removeEventListener('message', listener)
    }
  }
}

export default PostMessageSDK
