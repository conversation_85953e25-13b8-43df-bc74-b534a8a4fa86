.guide {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 998;
  background: rgba(0, 0, 0, 0.6);
  .guide_setting {
    position: fixed;
    left: 2.75rem;
    top: 3.125rem;
    width: 17.75rem;
  }

  .guide_title {
    position: fixed;
    left: 50%;
    transform: translateX(-50%);
    top: 3.125rem;
    width: 15.3125rem;
  }

  .guide_book {
    position: fixed;
    right: 3.125rem;
    top: 7.125rem;
    width: 15.8125rem;
  }
}

.dialogue {
  position: fixed;
  bottom: 0rem;
  top: 0rem;
  left: 0rem;
  right: 0rem;
  width: 100%;
  z-index: 2;
  img {
    position: absolute;
    bottom: 0;
    width: 100%;
    display: block;
  }
  .dialogue-content {
    position: absolute;
    top: 89%;
    left: 26.5%;
    color: #fff;
    width: 50%;
    height: 4.375rem;
    overflow: auto;
    span {
      color: #ffdd82;
    }
  }
}
