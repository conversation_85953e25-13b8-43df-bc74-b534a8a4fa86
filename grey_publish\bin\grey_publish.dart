import 'package:grey_publish/grey_publish.dart';
import 'package:ppt101_uc_utils/uc_utils.dart';

// 1、生成exe：dart compile exe .\grey_publish.dart
// 2、运行参数：[versionName, versionCode, 安装包url， 更新日志]
//    dart运行：dart .\bin\grey_publish.dart 1.0.2 3 https://gcdncs.101.com/v0.1/static/aic_earth_research/apk/earth_1.0.2_3.apk "1.中文\n2换行"
//    命令行运行：     .\bin\grey_publish.exe 1.0.2 3 https://gcdncs.101.com/v0.1/static/aic_earth_research/apk/earth_1.0.2_3.apk "1.中文\n2换行"
void main(List<String> args) async {
  print("main cmd: $args");

  UcTokenModel? token =
      await login("141028", "1017480db931a45fa0b6837920b5f328");
  print(token.toString());

  if (token == null) {
    print("token is null");
    return;
  }

  greyPublish(token, args);
}

void greyPublish(UcTokenModel token, List<String> args) async {
  String version = args[0];
  int versionCode = int.parse(args[1]);
  String downloadUrl = args[2];
  String updateLog = args[3];

  String description = version;
  DateTime now = DateTime.now();
  int releaseDate = now.millisecondsSinceEpoch;

  String extra = jsonVersionInfo(versionCode, version, downloadUrl, updateLog);

  int? versionId = await createVersion(
      token: token,
      version: version,
      versionCode: versionCode,
      description: description,
      releaseDate: releaseDate,
      extra: extra);
  if (versionId == null) {
    print("create version fail");
    return;
  }

  String taskName = "$version ${now.hour}:${now.minute}";
  int? statusCode =
      await createTask(token: token, taskName: taskName, versionId: versionId);
  print("create Task statusCode: $statusCode");
}
