import React from 'react'
import Loadable from 'react-loadable'
import Error from '../component/status/Error'

// eslint-disable-next-line import/prefer-default-export
export function asyncLoader(
  // eslint-disable-next-line max-len
  loadModule: () => Promise<React.ComponentType<unknown> | { default: React.ComponentType<unknown> }>
): JSX.Element {
  const LoadableComponent = Loadable({
    loader: loadModule,
    loading: ({ isLoading, error }): JSX.Element | null => {
      if (isLoading) return null
      if (error) return <Error error={error} />
      return null
    },
  })

  return <LoadableComponent />
}
