/* eslint-disable @typescript-eslint/no-unused-vars */
import React, { useEffect, useRef } from 'react'
import classNames from 'classnames/bind'
import { useAppDispatch, useAppSelector } from '@/store'
import IndexedDbSDK from '@/sdk/indexedDbSDK'
import _ from 'lodash'
import styles from './index.module.scss'
import {
  DEFAULT_DATA, STEP, updateConfig, updateData
} from './earthSlice'
import Saves from './components/Saves/Index'
import { PLAY_KEY } from './constant'
import Home from './components/Home/Index'
import Nodes from './components/Nodes/Index'

const cn = classNames.bind(styles)

const WordHome = () => {
  const { data, config } = useAppSelector((state) => state.earth)
  const { currentSaveId, step } = config
  const dispatch = useAppDispatch()
  const dbSDK = useRef<IndexedDbSDK>()

  const onLoadSave = async (saveId) => {
    if (!saveId) return
    dbSDK.current!.loadSave(saveId).then(async () => {
      const _data = await dbSDK.current?.getPlayData(PLAY_KEY)
      if (_data) {
        dispatch(updateData(_data.data))
      } else {
        // 兼容一下，避免创建库时没有创建数据
        dbSDK.current?.addPlayData({
          id: PLAY_KEY,
          data: DEFAULT_DATA,
        })
      }
      dispatch(updateConfig({ step: STEP.NODES }))
    }).catch((error) => {
      console.error('加载存档失败:', error)
    })
  }

  useEffect(() => {
    if (currentSaveId) {
      onLoadSave(currentSaveId)
    }
  }, [currentSaveId])

  const asyncDataToDb = async (_data, _config) => {
    if (config.currentSaveId) {
      const __data = await dbSDK.current?.getPlayData(PLAY_KEY)
      if (__data) {
        dbSDK.current?.updatePlayData(PLAY_KEY, {
          data: _data,
        })
      } else {
        dbSDK.current?.addPlayData({
          id: PLAY_KEY,
          data: _data,
        })
      }
    }
  }

  useEffect(() => {
    asyncDataToDb(data, config)
  }, [JSON.stringify(data), JSON.stringify(config)])

  useEffect(() => {
    dbSDK.current = IndexedDbSDK.getInstance()
  }, [])

  const renderStep = () => {
    switch (step) {
      case STEP.HOME:
        return <Home />
      case STEP.SAVES:
        return <Saves />
      case STEP.NODES:
        return <Nodes />
      default:
        return null
    }
  }

  return (
    <div className={cn('main')}>
      {
        renderStep()
      }
    </div>
  )
}

export default WordHome
