import React, { useState } from 'react'
import classNames from 'classnames/bind'
import { useAppDispatch, useAppSelector } from '@/store'
import {
  Popover, Radio
} from 'fish'
import iconBg from '@/asset/images/header_left/general_icon_bg.png' // 默认状态背景
import iconActBg from '@/asset/images/header_left/general_icon_bg_act.png' // 开启状态背景
import outBg from '@/asset/images/header_left/navigation_icon_out.png' // 退出按钮
import setBg from '@/asset/images/header_left/navigation_icon_set.png' // 默认设置按钮
import setOnBg from '@/asset/images/header_left/navigation_icon_set_on.png' // 开启状态设置按钮
import CustomSwitch from '@/component/chat/components/CustomSwitch'
import styles from './index.module.scss'
import { STEP, updateConfig, updateData } from '../../earthSlice'

const cn = classNames.bind(styles)

const SetConfig = ({ guided, onGuided }) => {
  const { data } = useAppSelector((state) => state.earth)
  const aiMode = data?.config?.aiMode
  const [visible, setVisible] = useState(false)

  const dispatch = useAppDispatch()

  const onChangeData = (newData) => {
    dispatch(updateData(newData))
  }

  const onExit = () => {
    dispatch(updateConfig({ step: STEP.SAVES, currentSaveId: '' }))
    dispatch(updateConfig({ currentSaveId: '' }))
  }
  const renderExitButton = () => (
    <div className={cn('exit-btn-wrapper')} onClick={onExit}>
      <img
        src={iconBg}
        className={cn('exit-btn-bg')}
        alt="背景"
      />
      <img
        src={outBg}
        className={cn('exit-btn-icon')}
        alt="退出"
      />
    </div>
  )

  const onSetting = () => {
    if (guided) {
      onGuided()
    }
  }

  const onChangeAiMode = () => {
    onChangeData({
      config: {
        ...(data?.config || {}),
        aiMode: !data?.config?.aiMode
      }
    })
  }

  // 连续点击计数器和定时器都用ref
  const settingClickCountRef = React.useRef(0)
  const settingClickTimeoutRef = React.useRef<number | null>(null)

  const handleSettingClick = () => {
    if (settingClickTimeoutRef.current) {
      clearTimeout(settingClickTimeoutRef.current)
    }
    settingClickCountRef.current += 1
    settingClickTimeoutRef.current = window.setTimeout(() => {
      settingClickCountRef.current = 0
    }, 300)
    if (settingClickCountRef.current >= 5) {
      settingClickCountRef.current = 0
      onChangeAiMode()
    }
    onSetting()
  }

  const renderSettingButton = () => (
    <div className={cn('setting-btn-wrapper', { active: visible })} onClick={handleSettingClick}>
      <img
        src={visible ? iconActBg : iconBg}
        className={cn('setting-btn-bg')}
        alt="背景"
      />
      <img
        src={visible ? setOnBg : setBg}
        className={cn('setting-btn-icon')}
        alt="设置"
      />
    </div>
  )

  return (
    <div className={cn('setting', { guided })}>
      {renderExitButton()}
      {guided ? renderSettingButton() : (
        <Popover
          overlayClassName={cn('setting-popover')}
          onVisibleChange={setVisible}
          content={(
            <div className={cn('setting-content', { aiMode })}>
              {
                aiMode && (
                  <>
                    <div className={cn('title')}>AI辅助深度</div>
                    <Radio.Group
                      size="large"
                      value={data?.config.aiLevel}
                      onChange={(e) => {
                        onChangeData({
                          config: {
                            ...(data?.config || {}),
                            aiLevel: e.target.value
                          }
                        })
                      }}
                    >
                      {[
                        {
                          value: 'high',
                          label: '高',
                          subLabel: '实验不成功即时提供帮助',
                        },
                        {
                          value: 'middle',
                          label: '中',
                          subLabel: '实验3次不成功，则AI主动提供帮助',
                        },
                        {
                          value: 'low',
                          label: '低',
                          subLabel: '实验5次不成功，则AI主动提供帮助',
                        },
                      ].map((option) => (
                        <Radio key={option.value} value={option.value} className={cn('label', { active: data?.config.aiLevel === option.value })}>
                          {option.label}
                          <div className={cn('sub_label')}>
                            {option.subLabel}
                          </div>
                        </Radio>
                      ))}
                    </Radio.Group>
                  </>
                )
              }
              <div>
                <div className={cn('title')}>成就系统</div>
                <div className={cn('achievement-switch')}>
                  <div className={cn('title')}>显示成就积分、勋章</div>
                  <CustomSwitch
                    className={cn('achievement-switch-toggle')}
                    checkedChildren="开"
                    unCheckedChildren="关"
                    checked={data?.config.achievement}
                    onChange={(value) => {
                      onChangeData({
                        config: {
                          ...(data?.config || {}),
                          achievement: value
                        }
                      })
                    }}
                  />
                </div>
              </div>
            </div>
          )}
          trigger="click"
          placement="bottom"
        >
          {renderSettingButton()}
        </Popover>
      )}

    </div>
  )
}

export default SetConfig
