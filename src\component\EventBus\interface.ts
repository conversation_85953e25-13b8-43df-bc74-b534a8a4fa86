import { <PERSON><PERSON><PERSON> } from '@/page/earth/components/Nodes/Achievement/constant'
import type { Events } from './constant'

export interface EventBusEvents {
  [Events.GuideHome]: () => void
  [Events.OpenComponent]: (data: { name: string, [key: string]: any }) => void
  [Events.CloseComponent]: (name) => void
  [Events.sendMessage]: (key, data) => void
  [Events.onSimulateSettingPanel]: (data) => void
  [Events.aiGuide]: (data, callback?: (_data: any) => void) => void
  [Events.fingerTip]: (data) => void
  [Events.ShowNotification]: (data) => void
  [Events.getClue]: (data) => void
  [Events.onLottie]: (data: {
    key: 'one' | 'two' | 'get' | 'in',
    level?: number,
    medal?: MedalKey,
  }) => void
}
