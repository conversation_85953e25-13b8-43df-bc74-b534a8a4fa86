{"compilerOptions": {"jsx": "react", "noEmit": true, "baseUrl": ".", "allowJs": true, "target": "esnext", "module": "esnext", "strict": true, "importHelpers": true, "noImplicitAny": false, "moduleResolution": "node", "experimentalDecorators": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "resolveJsonModule": true, "paths": {"@/*": ["src/*"], "fish": ["node_modules/@sdp.nd/fish"], "fish/*": ["node_modules/@sdp.nd/fish/*"], "uc-selector": ["node_modules/@sdp.nd/uc-selector"], "cloud-office-util": ["node_modules/@sdp.nd/cloud-office-util"], "cloud-office-util/*": ["node_modules/@sdp.nd/cloud-office-util/*"]}, "typeRoots": ["./node_modules/@types", "./node_modules/@gem-mine/cli-plugin-typescript/@types", "./src/@types"]}, "include": ["src", "./node_modules/@gem-mine/cli-plugin-typescript/@types"]}