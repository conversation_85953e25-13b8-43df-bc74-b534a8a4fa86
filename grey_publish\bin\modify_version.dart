import 'dart:io';
import 'package:git/git.dart';

void main(List<String> args) async {
  String version = args[0];
  String fileName = 'example/pubspec.yaml';

  String yamlContent = File(fileName).readAsStringSync();
  List<String> lines = yamlContent.split('\n');
  for (int i = 0; i < lines.length; i++) {
    String line = lines[i];
    if (line.startsWith('version: ')) {
      lines[i] = 'version: ${modifyVersion(version)}';
      break;
    }
  }
  File(fileName).writeAsStringSync(lines.join('\n'));

  pushToGit(fileName, version);
}

Future<void> pushToGit(String fileName, String message) async {
  GitDir gitDir = await GitDir.fromExisting(Directory.current.path);
  String branchName = (await gitDir.currentBranch()).branchName;
  String gerritBranchName = "HEAD:refs/heads/$branchName";
  print(gerritBranchName);
  await gitDir.runCommand(['add', fileName]);
  await gitDir.runCommand(['commit', '-m', message]);
  await gitDir.runCommand(['push', 'origin', gerritBranchName]);
}

String modifyVersion(String version) {
  if (version.contains('.')) {
    List<String> versionList = version.split('.');
    if (versionList.length == 4) {
      version = versionList.sublist(0, 3).join('.') + '+' + versionList[3];
    }
  }
  return version;
}
