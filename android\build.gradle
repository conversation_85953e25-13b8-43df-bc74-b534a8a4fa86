// Top-level build file where you can add configuration options common to all sub-projects/modules.
apply from: "variables.gradle"

buildscript {
    repositories {
        google()
        mavenCentral()
        // 华为 Maven 仓库
        maven { url 'https://developer.huawei.com/repo/'}
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:7.4.2'
        classpath 'com.google.gms:google-services:4.3.5'
        classpath 'io.sentry:sentry-android-gradle-plugin:3.12.0'

        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}


allprojects {
    repositories {
        google()
        mavenCentral()
        // 华为 Maven 仓库
        maven { url 'https://developer.huawei.com/repo/'}
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}
