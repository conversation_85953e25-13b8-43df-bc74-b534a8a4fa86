/* eslint-disable no-bitwise */
/* eslint-disable no-param-reassign */
import React, {
  PropsWithChildren,
  forwardRef,
  useEffect, useImperativeHandle, useRef,
} from 'react'
import lottieWeb, {
  AnimationConfigWithData, AnimationConfigWithPath, AnimationItem,
} from 'lottie-web'
import classNames from 'classnames/bind'
import styles from './index.module.scss'
import { LottiePlayerParams } from './interface'
import AudioService from './AudioService'

const cn = classNames.bind(styles)

const AudioKey = 'lottie_audio'

type IProps = PropsWithChildren<{
  onComplete?: (key: string) => void
  onLoad?: (key: string) => void
  onError?: (errMsg: string) => void
}>

export interface IRefs {
  playLottie: any
  pauseLottie: any
  stopLottie: any
  loadLottie: any
  destroyLottie: any
  seekLottie: any
}

type AnimationItemWithFrameSegment = AnimationItem & {
  frameByIndex?: number[]
}

const LottiePlayer = forwardRef<IRefs, IProps>((props, ref) => {
  const {
    onComplete: propOnComplete, onLoad, onError,
  } = props
  const container = useRef<HTMLDivElement>(null)
  const lotties = useRef<{
    [key: string]: AnimationItemWithFrameSegment | null
  }>({})
  const svgContainers = useRef<{
    [key: string]: HTMLDivElement | null
  }>({})

  const playingAudioKeys = useRef<string[]>([])
  const getAudioKey = (params: LottiePlayerParams) => `${AudioKey}_${params.key}`

  const stopLottie = (params: LottiePlayerParams) => {
    const { key } = params
    const audioKey = getAudioKey(params)
    const lottie = lotties.current[key]
    if (lottie) {
      lottie.goToAndStop(0)
    }
    AudioService.audioStop({ key: audioKey })
    playingAudioKeys.current = playingAudioKeys.current.filter((k) => k !== audioKey)
  }

  const destroyLottie = (params: LottiePlayerParams) => {
    const { key } = params
    const audioKey = getAudioKey(params)
    const lottie = lotties.current[key]
    if (lottie) {
      lottie.destroy()
      lotties.current[key] = null
      console.warn('[lottie-player] lottie实例销毁', key)
    }
    const oldSvgContainer = svgContainers.current[key]
    if (oldSvgContainer) {
      container.current?.removeChild(oldSvgContainer)
      svgContainers.current[key] = null
    }
    AudioService.audioStop({ key: audioKey })
    playingAudioKeys.current = playingAudioKeys.current.filter((k) => k !== audioKey)
    // 清除body下的直接span元素
    const bodySpans = document.body.querySelectorAll(':scope > span')
    bodySpans.forEach((span) => span.remove())
  }

  const loadLottie = async (params: LottiePlayerParams) => {
    if (!container.current) {
      return
    }
    const {
      key,
      json,
      audio,
      volume,
      loop = false,
      left = 0,
      top = 0,
      width = '100%',
      height = '100%',
      zIndex = 0,
    } = params
    if (!key || !json) {
      return
    }

    destroyLottie(params)
    const styleWidth = typeof width === 'number' ? `${width}px` : width
    const styleHeight = typeof height === 'number' ? `${height}px` : height
    const styleLeft = typeof left === 'number' ? `${left}px` : left
    const styleTop = typeof top === 'number' ? `${top}px` : top

    const svgContainer = document.createElement('div')
    container.current.appendChild(svgContainer)
    svgContainer.setAttribute('id', 'svgContainer')
    svgContainer.setAttribute('style', `position: absolute; left: ${styleLeft}; top: ${styleTop}; width: ${styleWidth}; height: ${styleHeight}; z-index: ${zIndex};`)
    const config: AnimationConfigWithData<'svg'> | AnimationConfigWithPath<'svg'> = {
      container: svgContainer,
      renderer: 'svg',
      loop,
      autoplay: false,
    };
    (config as AnimationConfigWithData<'svg'>).animationData = json
    const newLottie: AnimationItemWithFrameSegment = lottieWeb.loadAnimation(config)

    newLottie.addEventListener('DOMLoaded', () => {
      console.log('[lottie-player] load DOMLoaded')

      newLottie.play()
      if (propOnComplete) {
        newLottie.addEventListener('complete', (e) => {
          console.log('[lottie-player] complete', e, newLottie)
          propOnComplete(key)
        })
      }
      // 避免音画不同步
      if (audio) {
        const audioKey = getAudioKey(params)
        AudioService.audioLoad({
          key: audioKey,
          url: audio,
        })
        AudioService.audioVolume({
          key: audioKey,
          volume: volume ?? 1,
        })
        AudioService.audioPlay({ key: audioKey })
        playingAudioKeys.current.push(audioKey)
      }
      onLoad?.(key)
    })
    newLottie.addEventListener('error', () => {
      console.error('[lottie-player] load error')
      onError?.('Lottie加载失败')
    })
    lotties.current[key] = newLottie
    svgContainers.current[key] = svgContainer
  }

  const playLottie = (params: LottiePlayerParams) => {
    const {
      key, seek,
    } = params
    const audioKey = getAudioKey(params)
    const lottie = lotties.current[key]
    if (!lottie) {
      return
    }
    if (seek) {
      lottie.goToAndPlay(seek / 1000)
      AudioService.audioSeek({
        key: audioKey,
        time: seek / 1000,
      })
    } else {
      lottie.play()
    }
    AudioService.audioPlay({ key: audioKey })
    playingAudioKeys.current.push(audioKey)
  }

  const pauseLottie = (params: LottiePlayerParams) => {
    const { key } = params
    const audioKey = getAudioKey(params)
    const lottie = lotties.current[key]
    if (!lottie) {
      return
    }
    AudioService.audioPause({ key: audioKey })
    lottie.pause()
  }

  const seekLottie = (params: LottiePlayerParams) => {
    const {
      key, seek,
    } = params
    const audioKey = getAudioKey(params)
    const lottie = lotties.current[key]
    if (!lottie || typeof seek === 'undefined') {
      return
    }
    if (lottie.isPaused) {
      lottie.goToAndStop(seek / 1000)
    } else {
      lottie.goToAndPlay(seek / 1000)
    }
    AudioService.audioSeek({
      key: audioKey,
      time: seek / 1000,
    })
  }

  useEffect(() => () => {
    playingAudioKeys.current.forEach((key) => AudioService.audioStop({ key }))
    playingAudioKeys.current = []
  }, [])

  useImperativeHandle(ref, () => ({
    playLottie,
    pauseLottie,
    stopLottie,
    loadLottie,
    destroyLottie,
    seekLottie
  }))

  return (
    <div
      ref={container}
      className={cn('lottie-container')}
    />
  )
})

export default LottiePlayer
