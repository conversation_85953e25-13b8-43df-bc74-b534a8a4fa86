<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_dialog_round"
    android:gravity="center_horizontal"
    android:orientation="vertical"
    android:padding="24dp">

    <TextView
        android:id="@+id/update_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="12dp"
        android:gravity="left|center_vertical"
        android:textAlignment="viewStart"
        android:textColor="#000000"
        android:textSize="20sp"
        android:textStyle="bold"
        tools:ignore="RtlCompat,RtlHardcoded" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginBottom="12dp"
        android:layout_weight="1">

        <TextView
            android:id="@+id/update_message"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="left|center_vertical"
            android:textAlignment="viewStart"
            android:textColor="#000000"
            android:textSize="16sp"
            tools:ignore="RtlCompat,RtlHardcoded" />
    </ScrollView>

    <LinearLayout
        style="?android:attr/buttonBarStyle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="end"
        android:orientation="horizontal">

        <Button
            android:id="@+id/update_cancel"
            style="?android:attr/buttonBarButtonStyle"
            android:layout_width="wrap_content"
            android:layout_height="42dp"
            android:background="@android:color/transparent"
            android:minWidth="0dp"
            android:minHeight="0dp"
            android:paddingLeft="16dp"
            android:paddingTop="0dp"
            android:paddingRight="16dp"
            android:paddingBottom="0dp"
            android:text="@string/update_cancel"
            android:textColor="#80000000"
            android:textSize="16sp"/>

        <Button
            android:id="@+id/update_confirm"
            style="?android:attr/buttonBarButtonStyle"
            android:layout_width="wrap_content"
            android:layout_height="42dp"
            android:layout_marginStart="8dp"
            android:background="@android:color/transparent"
            android:minWidth="0dp"
            android:minHeight="0dp"
            android:paddingLeft="16dp"
            android:paddingTop="0dp"
            android:paddingRight="16dp"
            android:paddingBottom="0dp"
            android:text="@string/update_confirm"
            android:textSize="16sp"
            android:textColor="#2196F3" />
    </LinearLayout>
</LinearLayout>
