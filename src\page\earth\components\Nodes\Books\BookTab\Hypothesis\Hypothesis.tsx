/* eslint-disable react/no-array-index-key */
import React, {
  FC, useMemo, useState, useRef
} from 'react'
import classNames from 'classnames/bind'
import { HTML5Backend } from 'react-dnd-html5-backend'
import { TouchBackend } from 'react-dnd-touch-backend'
import { DndProvider, TouchTransition, MouseTransition } from 'react-dnd-multi-backend'
import { v4 as uuidv4 } from 'uuid'
import { IEarthData, UPGRADE_KEY } from '@/page/earth/interface'
import {
  Divider, Form, Input, Overlay, Popconfirm
} from 'fish'
import _ from 'lodash'
import { useAppSelector } from '@/store'
import { useUpdateMedals } from '@/page/earth/hooks/useAchievement'
import clueEditClosePng from '@/asset/images/books/hypothesis/source_imgs/clue_edit_close.png'
import sourceHeaderBgPng from '@/asset/images/books/hypothesis/source_imgs/source_header_bg.png'
import sourceHeaderKeyPng from '@/asset/images/books/hypothesis/source_imgs/clue_header_key.png'
import pagebreakOnPng from '@/asset/images/books/hypothesis/source_imgs/pagebreak_on.png'
import pagebreakOffPng from '@/asset/images/books/hypothesis/source_imgs/pagebreak_off.png'
import inputSuffixPng from '@/asset/images/books/hypothesis/target_imgs/input_suffix.png'
import inputSentPng from '@/asset/images/books/hypothesis/target_imgs/input_sent.png'
import iconDelete from '@/asset/images/books/hypothesis/target_imgs/list_delete_icon.png'
import iconAdd from '@/asset/images/books/hypothesis/target_imgs/list_add_icon.png'
import headerBottom from '@/asset/images/books/hypothesis/target_imgs/header_bottom.png'
import deleteIcon from '@/asset/images/books/hypothesis/target_imgs/delete_icon.png'
import divergeEntrancePng from '@/asset/images/books/hypothesis/target_imgs/diverge_entrance_icon.png'
import EventBus from '@/component/EventBus'
import { Events } from '@/component/EventBus/constant'
import { getElementPosition } from '@/util/box'
import AudioManager from '@/util/AudioManager'
import styles from './index.module.scss'
import { CustomDragPreview, SourceBox, TargetBox } from './DndComponent'
import { BOOK_GUIDE_MAP, DEFAULT_HY_TAB, LABEL_MAP } from '../../constant'
import Wheel from './Wheel'
import { MedalKey } from '../../../Achievement/constant'

const cn = classNames.bind(styles)

interface IHypothesisProps {
  data: IEarthData['books']['hypothesis']
  onChange: (data) => void
}
const Hypothesis: FC<IHypothesisProps> = ({
  data,
  onChange
}) => {
  const { data: storeData } = useAppSelector((state) => state.earth)
  const { updateGrade } = useUpdateMedals()
  const { questionUnLock = false, guided } = storeData || {}
  const [itemModal, setItemModal] = useState<any>({})
  const [activeKey, setActiveKey] = useState(DEFAULT_HY_TAB)
  const [wheelData, setWheelData] = useState({ index: -1, visible: false })
  const [dragId, setDragId] = useState('')
  const tabListRef = useRef<HTMLDivElement>(null)

  const activeIndex = useMemo(() => {
    if (activeKey === DEFAULT_HY_TAB) return -1
    return _.findIndex(data.presumes, (item) => item.id === activeKey)
  }, [activeKey, data.presumes])
  // 删除模式相关状态
  const [isDeleteMode, setIsDeleteMode] = useState(false)
  const [deleteModeData, setDeleteModeData] = useState<any>(null)

  // 分页相关状态
  const [currentPage, setCurrentPage] = useState(0)
  const containerRef = useRef<HTMLDivElement>(null)
  const touchStartX = useRef<number>(0)
  const touchEndX = useRef<number>(0)
  const isSwipingRef = useRef<boolean>(false)
  const isDraggingRef = useRef<boolean>(false)

  const ITEMS_PER_PAGE = 6 // 3行2列 = 6个项目
  const clueData = data.clue || []
  const totalPages = Math.ceil(clueData.length / ITEMS_PER_PAGE)

  // 获取当前页的数据
  const getCurrentPageData = () => {
    const startIndex = currentPage * ITEMS_PER_PAGE
    return clueData.slice(startIndex, startIndex + ITEMS_PER_PAGE)
  }

  // 触摸事件处理
  const handleTouchStart = (e: React.TouchEvent) => {
    touchStartX.current = e.touches[0].clientX
    isSwipingRef.current = false
    // 重置拖拽状态
    isDraggingRef.current = false
  }

  const handleTouchMove = (e: React.TouchEvent) => {
    // 如果正在拖拽，不处理滑动
    if (isDraggingRef.current) return

    touchEndX.current = e.touches[0].clientX
    const deltaX = Math.abs(touchStartX.current - touchEndX.current)

    // 如果滑动距离超过阈值，则认为是滑动操作
    if (deltaX > 10) {
      isSwipingRef.current = true
    }
  }

  const handleTouchEnd = () => {
    // 如果正在拖拽或刚结束拖拽，不执行翻页逻辑
    if (isDraggingRef.current || dragId) {
      isSwipingRef.current = false
      return
    }

    if (!isSwipingRef.current) return

    const deltaX = touchStartX.current - touchEndX.current
    const minSwipeDistance = 50
    if (Math.abs(deltaX) > minSwipeDistance) {
      if (deltaX > 0 && currentPage < totalPages - 1) {
        // 向左滑动，下一页
        setCurrentPage(currentPage + 1)
      } else if (deltaX < 0 && currentPage > 0) {
        // 向右滑动，上一页
        setCurrentPage(currentPage - 1)
      }
    }

    // 重置状态
    isSwipingRef.current = false
  }

  const isMobile = useMemo(() => typeof window !== 'undefined' && /Mobi|Android|iPhone|iPad|iPod|Mobile/i.test(navigator.userAgent), [])
  // 页面指示器点击处理
  const handlePageIndicatorClick = (pageIndex: number) => {
    if (isMobile) {
      // 移动端拦截
      return
    }
    setCurrentPage(pageIndex)
  }
  const HTML5toTouch = {
    backends: [
      {
        id: 'html5',
        backend: HTML5Backend,
        transition: MouseTransition,
      },
      {
        id: 'touch',
        backend: TouchBackend,
        options: { enableMouseEvents: true },
        preview: true,
        transition: TouchTransition,
      },
    ],
  }

  // 线索描述编辑
  const onShowItem = (item) => {
    setItemModal({
      visible: true,
      item
    })
  }
  const onChangeItem = (e) => {
    const newItem = {
      ...itemModal,
      item: {
        ...itemModal.item,
        des: e.target.value
      }
    }
    setItemModal(newItem)
  }

  const onChangeItemDone = () => {
    onChange({
      clue: data.clue.map((item) => {
        if (item.id === itemModal.item.id) {
          return {
            ...item,
            des: itemModal.item.des
          }
        }
        return item
      })
    })
    setItemModal({})
  }
  // 关联线索图片
  const getRelationImg = (rc) => {
    const clue = (data.clue || []).find((item) => item.id === rc)
    return clue?.img
  }

  // 假设1
  const onChangeQp = (index, value) => {
    const newData = _.cloneDeep(data.question_presumes)
    newData[index] = {
      ...newData[index],
      ...value
    }
    onChange({ question_presumes: newData })
    if (value.presume) {
      updateGrade(MedalKey.earth_researcher, { [UPGRADE_KEY.hypothesis]: true })
    } else if (value.question) {
      updateGrade(MedalKey.earth_researcher, { [UPGRADE_KEY.question]: true })
    }
  }
  const onAddQp = () => {
    onChange({
      question_presumes: data.question_presumes.concat({
        id: uuidv4(),
        question: '',
        presume: '',
        relation_clue: ['', '', ''] // 渲染3个表单
      })
    })
  }

  const onDeleteMode = () => {
    if (!isDeleteMode) {
      // 进入删除模式：复制数据用于临时删除
      const originalQuestionPresumes = _.cloneDeep(data.question_presumes)
      setDeleteModeData(originalQuestionPresumes)
      setIsDeleteMode(true)
    }
  }

  // 确认删除
  const onConfirmDelete = () => {
    onChange({ question_presumes: deleteModeData })
    setIsDeleteMode(false)
    setDeleteModeData(null)
  }

  // 取消删除
  const onCancelDelete = () => {
    setIsDeleteMode(false)
    setDeleteModeData(null)
  }

  // 删除单个问题项
  const onDeleteQuestionItem = (index: number) => {
    const newData = _.cloneDeep(deleteModeData)
    newData.splice(index, 1)
    setDeleteModeData(newData)
  }

  const onDropQp = (value, index, rc_index) => {
    const newData = _.cloneDeep(data.question_presumes)
    _.set(newData, `[${index}].relation_clue[${rc_index}]`, value)
    if (rc_index >= 2) {
      _.set(newData, `[${index}].relation_clue[${rc_index + 1}]`, '')
      setTimeout(() => {
        const box = document.getElementById(`box_${newData[index].id}`)!
        box.scrollLeft = box.scrollWidth
      }, 0)
    }
    onChange({ question_presumes: newData })
  }

  // 假设2以上
  const reversePreSumes = useMemo(() => _.reverse((data.presumes || []).map((item, index) => ({ ...item, index }))), [JSON.stringify(data.presumes)])

  const onChangeP = (id, value) => {
    const newData = _.cloneDeep(data.presumes)
    onChange({
      presumes: newData.map((item) => {
        if (id === item.id) {
          return {
            ...item,
            ...value
          }
        }
        return item
      })
    })
  }

  const onAddPresumes = () => {
    const id = uuidv4()
    const newPresumes = data.presumes.concat({
      id,
      out: {
        value: '',
        relation_clue: ['', '', '']
      },
      middle: {
        value: '',
        relation_clue: ['', '', '']
      },
      in: {
        value: '',
        relation_clue: ['', '', '']
      },
    })
    onChange({
      presumes: newPresumes
    })
    if (newPresumes.length > 2 && !guided.slide_tab) {
      const position = getElementPosition(tabListRef.current!)
      EventBus.emit(Events.fingerTip, {
        text: '',
        direction: 'slide',
        position: { x: position.x + 80, y: position.y - 10 },
        guideKey: 'slide_tab'
      })
    }
    setActiveKey(id)
    updateGrade(MedalKey.earth_researcher, { [UPGRADE_KEY.iterate_hypothesis]: true })
  }

  const onDeleteP = (id) => {
    const newData = _.cloneDeep(data.presumes)
    const index = newData.findIndex((item) => item.id === id)!
    onChange({ presumes: newData.filter((item) => item.id !== id) })
    setActiveKey(newData[index - 1]?.id)
  }

  const onDropP = (value, id, key, rc_index) => {
    const newData = _.cloneDeep(data.presumes)
    const index = _.findIndex(newData, (item) => item.id === id)
    _.set(newData, `[${index}][${key}].relation_clue[${rc_index}]`, value)
    if (rc_index >= 2) {
      _.set(newData, `[${index}][${key}].relation_clue[${rc_index + 1}]`, '')
      setTimeout(() => {
        const box = document.getElementById(`box_${newData[index].id}`)!
        box.scrollLeft = box.scrollWidth
      }, 0)
    }
    onChange({ presumes: newData })
  }

  const onShowWheel = (e, index) => {
    e.stopPropagation()
    setWheelData({
      index,
      visible: true
    })
    EventBus.emit(Events.aiGuide, BOOK_GUIDE_MAP.diverge1, () => {
      EventBus.emit(Events.aiGuide, BOOK_GUIDE_MAP.diverge2, () => {
        EventBus.emit(Events.aiGuide, BOOK_GUIDE_MAP.diverge3)
      })
    })
  }

  return (
    <div className={cn('hypothesis')}>
      <DndProvider options={HTML5toTouch}>
        <div className={cn('sources')}>
          <div className={cn('source-title-box')}>
            <span className={cn('source-title')}>
              线索
              <img className={cn('source-key')} src={sourceHeaderKeyPng} />
            </span>
            <span className={cn('source-des')}>
              <img src={sourceHeaderBgPng} />
              拖动线索图片，至右侧「线索框」里，跟假设关联起来！
            </span>
          </div>
          <div
            className={cn('source-list')}
            ref={containerRef}
            onTouchStart={handleTouchStart}
            onTouchMove={handleTouchMove}
            onTouchEnd={handleTouchEnd}
            key={currentPage} // 添加key来触发重新渲染动画
          >
            {
              getCurrentPageData().map((item) => (
                <div className={cn('source-item', { dragging: dragId === item.id })} key={item.id}>
                  <SourceBox
                    id={item.id}
                    img={item.img}
                    key={item.id}
                    onDrag={(id) => {
                      setDragId(id)
                      isDraggingRef.current = true
                    }}
                    onEnd={() => {
                      setDragId('')
                      // 延迟重置拖拽状态，确保 handleTouchEnd 能正确检测到拖拽状态
                      setTimeout(() => {
                        isDraggingRef.current = false
                      }, 100)
                    }}
                  >
                    <img src={item.img} />
                  </SourceBox>
                  <div
                    className={cn('source-des')}
                    onClick={() => {
                      onShowItem(item)
                    }}
                  >
                    {item.title}
                  </div>
                </div>
              ))
            }
          </div>
          {totalPages > 1 && (
            <div className={cn('page-indicators')}>
              {Array.from({ length: Math.min(totalPages, 10) }, (__, index) => {
                // 计算实际的页面索引
                let actualPageIndex
                if (totalPages <= 10) {
                  // 总页数不超过10，直接显示
                  actualPageIndex = index
                } else if (currentPage < 10) {
                  // 当前页小于10，显示前10页
                  actualPageIndex = index
                } else {
                  // 当前页大于等于10，显示当前页前面的9页加当前页
                  actualPageIndex = currentPage - 9 + index
                }
                return (
                  <img
                    src={
                      actualPageIndex === currentPage ? pagebreakOnPng : pagebreakOffPng
                    }
                    key={actualPageIndex}
                    onClick={() => handlePageIndicatorClick(actualPageIndex)}
                  />
                )
              })}
            </div>
          )}

        </div>
        <div className={cn('target')}>
          <div className={cn('custom-tabs')}>
            {/* Tab 头部 */}
            <div className={cn('tab-header')}>
              <div className={cn('tab-list')} ref={tabListRef}>
                {
                  questionUnLock && reversePreSumes.map((presume, index) => (
                    <div
                      key={presume.id}
                      className={cn('tab-item', { first: index === 0 }, { active: activeKey === presume.id })}
                      onClick={() => setActiveKey(presume.id)}
                      style={{
                        left: `${-index}rem`,
                        zIndex: reversePreSumes.length - index + 1
                      }}
                    >
                      {`假设${presume.index + 2}.0`}
                    </div>
                  ))
                }
                <div
                  className={cn('tab-item', 'one', { first: reversePreSumes.length === 0 }, { active: activeKey === DEFAULT_HY_TAB })}
                  onClick={() => setActiveKey(DEFAULT_HY_TAB)}
                  style={{
                    left: `${-reversePreSumes.length}rem`,
                    zIndex: 1
                  }}
                >
                  问题和假设1.0
                </div>
              </div>
              {questionUnLock && (
                <div className={cn('tab-extra')} onClick={onAddPresumes} />
              )}
              <img src={headerBottom} />
            </div>

            {/* Tab 内容 */}
            <div className={cn('tab-content')}>
              {
                questionUnLock && reversePreSumes.map((presume) => (
                  <div
                    key={presume.id}
                    className={cn('tab-pane', { active: activeKey === presume.id })}
                    style={{ display: activeKey === presume.id ? 'block' : 'none' }}
                  >
                    <div className={cn('hy-form-content', 'form-p')}>
                      <div className={cn('form-title')}>
                        <span>地球内部存在三层结构，其特点如下：</span>
                        {presume.index > 0 && (
                          <Popconfirm
                            placement="bottomRight"
                            title={`是否删除[假设${activeIndex + 2}.0]？`}
                            onConfirm={() => { onDeleteP(presume.id) }}
                            overlayClassName={cn('custom-popconfirm')}
                            icon={null}
                          >
                            <img src={iconDelete} />
                          </Popconfirm>
                        )}
                      </div>
                      {
                        Object.keys(LABEL_MAP).map((key) => {
                          const first_empty_rc = presume[key]?.relation_clue.findIndex((c) => !c)
                          return (
                            <Form className={cn('form-group')} labelCol={{ span: 6 }} wrapperCol={{ span: 18 }} key={key}>
                              <Form.Item label={LABEL_MAP[key]}>
                                <Input placeholder="提问 ：" value={presume[key]?.value} onChange={(e) => { onChangeP(presume.id, { [key]: { ...presume[key], value: e.target.value } }) }} />
                              </Form.Item>
                              <Form.Item label="关联线索">
                                <div className={cn('relation-box')} id={`box_${presume.id}`}>
                                  {
                                    presume[key]?.relation_clue.map((rc, rc_index) => (
                                      <TargetBox
                                        key={rc_index}
                                        can={first_empty_rc === rc_index}
                                        onDrop={({ id: rc_id }) => {
                                          onDropP(rc_id, presume.id, key, rc_index)
                                        }}
                                        hasRc={rc}
                                      >
                                        <div className={cn('relation-clue')}>
                                          {rc && (
                                            <img
                                              src={deleteIcon}
                                              className={cn('relation-delete-icon')}
                                              onClick={() => {
                                                onDropP('', presume.id, key, rc_index)
                                              }}
                                            />
                                          )}
                                          {rc && <img className={cn('relation-clue-img')} src={getRelationImg(rc)} />}
                                        </div>
                                      </TargetBox>
                                    ))
                                  }
                                </div>
                              </Form.Item>
                              {key !== 'in' && <Divider />}
                            </Form>
                          )
                        })
                      }
                    </div>
                  </div>
                ))
              }
              <div
                className={cn('tab-pane', { active: activeKey === DEFAULT_HY_TAB })}
                style={{ display: activeKey === DEFAULT_HY_TAB ? 'block' : 'none' }}
              >
                <div className={cn('hy-form-content')}>
                  <div className={cn('form-qp')}>
                    {
                      (isDeleteMode ? deleteModeData : data.question_presumes).map((item, index) => {
                        const first_empty_rc = item.relation_clue?.findIndex((c) => !c)
                        return (
                          <div key={item.id} className={cn('form-group-wrapper', { 'delete-mode': isDeleteMode })}>
                            {isDeleteMode && (
                              <img src={deleteIcon} className={cn('delete-icon')} onClick={() => onDeleteQuestionItem(index)} />
                            )}
                            <Form
                              className={cn('form-group')}
                              labelCol={{ span: 6 }}
                              wrapperCol={{ span: 18 }}
                              key={item.id}
                              colon={false}
                            >
                              <Form.Item label={(
                                <span>
                                  问题
                                  {!isDeleteMode && (
                                    <img
                                      style={{ width: '2.5rem' }}
                                      src={divergeEntrancePng}
                                      onClick={(e) => {
                                        onShowWheel(e, index)
                                      }}
                                    />
                                  )}
                                </span>
                              )}
                              >
                                <Input
                                  size="large"
                                  suffix={!isDeleteMode ? (
                                    <div
                                      onClick={(e) => {
                                        e.stopPropagation()
                                        const inputElement = e.currentTarget.closest('.fish-input-affix-wrapper')?.querySelector('input')
                                        const currentValue = inputElement?.value || ''
                                        onChangeQp(index, { question: currentValue })
                                      }}
                                    >
                                      <img src={inputSuffixPng} />
                                      <img src={inputSentPng} />
                                    </div>
                                  ) : undefined}
                                  key={item.question}
                                  defaultValue={item.question}
                                  disabled={isDeleteMode}
                                />
                              </Form.Item>
                              {questionUnLock && (
                                <Form.Item label="假设">
                                  <Input
                                    size="large"
                                    suffix={!isDeleteMode ? (
                                      <div onClick={(e) => {
                                        e.stopPropagation()
                                        const inputElement = e.currentTarget.closest('.fish-input-affix-wrapper')?.querySelector('input')
                                        const currentValue = inputElement?.value || ''
                                        onChangeQp(index, { presume: currentValue })
                                      }}
                                      >
                                        <img src={inputSuffixPng} />
                                        <img src={inputSentPng} />
                                      </div>
                                    ) : undefined}
                                    defaultValue={item.presume}
                                    key={item.presume}
                                    onBlur={() => {
                                      if (!guided.presume) {
                                        const position = getElementPosition(document.getElementById(`box_${item.id}_0`)!)
                                        EventBus.emit(Events.fingerTip, {
                                          text: '将与该假设相关的线索拖入此处',
                                          direction: 'left',
                                          position: { x: position.x + 30, y: position.y },
                                          guideKey: 'presume'
                                        })
                                      }
                                    }}
                                    disabled={isDeleteMode}
                                  />
                                </Form.Item>
                              )}
                              {questionUnLock && (
                                <Form.Item label="关联线索">
                                  <div className={cn('relation-box')} id={`box_${item.id}`}>
                                    {
                                      item.relation_clue.map((rc, rc_index) => (
                                        <TargetBox
                                          key={rc_index}
                                          can={!isDeleteMode && rc_index === first_empty_rc}
                                          onDrop={({ id: rc_id }) => {
                                            if (!isDeleteMode) {
                                              onDropQp(rc_id, index, rc_index)
                                            }
                                          }}
                                          hasRc={rc}
                                        >
                                          <div className={cn('relation-clue')} id={`box_${item.id}_${rc_index}`}>
                                            {rc && !isDeleteMode && (
                                              <img
                                                src={deleteIcon}
                                                className={cn('relation-delete-icon')}
                                                onClick={() => {
                                                  onDropQp('', index, rc_index)
                                                }}
                                              />
                                            )}
                                            {rc && <img className={cn('relation-clue-img')} src={getRelationImg(rc)} />}
                                          </div>
                                        </TargetBox>
                                      ))
                                    }
                                  </div>
                                </Form.Item>
                              )}
                            </Form>
                          </div>
                        )
                      })
                    }
                  </div>
                  <div className={cn('form-groups-btns')}>
                    {isDeleteMode ? (
                      <>
                        <div className={cn('cancel-btn')} onClick={onCancelDelete}>
                          取消
                        </div>
                        <div className={cn('confirm-btn')} onClick={onConfirmDelete}>
                          确认
                        </div>
                      </>
                    ) : (
                      <>
                        <div className={cn('delete-btn')} onClick={onDeleteMode}>
                          <img src={iconDelete} />
                          删除问题
                        </div>
                        <div className={cn('add-btn')} onClick={onAddQp}>
                          <img src={iconAdd} />
                          添加问题
                        </div>
                      </>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
          {wheelData.visible && (
            <Wheel
              onClose={() => {
                AudioManager.getInstance('sfx').stop()

                setWheelData({
                  index: -1,
                  visible: false
                })
              }}
              onChange={(value) => {
                AudioManager.getInstance('sfx').stop()
                onChangeQp(wheelData.index, { question: value })
                setWheelData({
                  index: -1,
                  visible: false
                })
              }}
              getRelationImg={getRelationImg}
            />
          )}
        </div>
        {isMobile && <CustomDragPreview />}
      </DndProvider>
      <Overlay
        visible={itemModal.visible}
        title={null}
        onCancel={() => {
          onChangeItemDone()
          setItemModal({})
        }}
        wrapClassName={cn('overlay-wrap')}
      >
        <div className={cn('overlay-item')}>
          <div className={cn('title')}>
            编辑线索
          </div>
          <img
            src={clueEditClosePng}
            className={cn('close')}
            onClick={() => {
              onChangeItemDone()
              setItemModal({})
            }}
          />
          <div className={cn('content')}>
            <div className={cn('img-bg')}>
              <img src={itemModal.item?.img} className={cn('img')} />
            </div>
            <Input.TextArea
              value={itemModal.item?.des}
              onChange={onChangeItem}
              rows={2}
              className={cn('textarea')}
            />
          </div>
        </div>
      </Overlay>
    </div>
  )
}

export default Hypothesis
