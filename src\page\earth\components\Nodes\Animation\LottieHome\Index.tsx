/* eslint-disable @typescript-eslint/no-use-before-define */
import React, { useEffect, useRef, useState } from 'react'
import EventBus from '@/component/EventBus'
import { Events } from '@/component/EventBus/constant'
import oneJson from '@/asset/lottie/medal_one.json'
import twoJson from '@/asset/lottie/medal_two.json'
import getJson from '@/asset/lottie/score_get.json'
import inJson from '@/asset/lottie/score_in.json'
import classNames from 'classnames/bind'
import { sleep } from 'cloud-office-util/help'
import _ from 'lodash'
import { getElementPosition } from '@/util/box'
import styles from './index.module.scss'
import LottiePlayer, { IRefs } from './LottiePlayer'
import useTransitionAnimation from '../Screenshot/useTransitionAnimation'
import { LEVEL_MEDALS_ICONS, MEDAL_LIST } from '../../Achievement/constant'

const cn = classNames.bind(styles)

const LottieHome = () => {
  const containRef = useRef<HTMLDivElement>(null)
  const lottieRef = useRef<IRefs>(null)
  const [play, setPlay] = useState(false)
  const queueRef = useRef<any[]>([])
  const processingRef = useRef(false)

  const {
    animateTo, resetElement
  } = useTransitionAnimation()

  // 处理队列中的下一个任务
  const processNextInQueue = async () => {
    if (processingRef.current || queueRef.current.length === 0) {
      return
    }

    processingRef.current = true
    const nextData = queueRef.current.shift()
    await executeLottieAnimation(nextData)
  }

  // 执行具体的动画逻辑
  const executeLottieAnimation = async (_data) => {
    setPlay(true)
    await sleep(50)
    const duration = 2
    if (_data.key === 'get') {
      lottieRef.current?.loadLottie({
        key: _data.key,
        json: getJson,
        loop: true
      })
      const position = getElementPosition(document.getElementById('score-icon')!)
      const iconWidth = document.getElementById('score-icon')!.clientWidth
      const htmlFontSize = parseFloat(getComputedStyle(document.documentElement).fontSize)

      const width = 320
      animateTo(containRef.current, {
        x: position.x - width / 2 - iconWidth * 0.3,
        y: position.y - width / 2,
        width,
        height: width,
        opacity: 0.3,
        duration,
      })
      await sleep(duration * 1000) // 等待动画完成
      lottieRef.current?.destroyLottie(_data)
      resetElement(containRef.current)

      const width2 = (100 / 16) * htmlFontSize
      lottieRef.current?.loadLottie({
        key: 'in',
        json: inJson,
        width: width2,
        height: width2,
        loop: false,
        left: position.x - iconWidth * 0.75,
        top: position.y - width2 / 2,
      })
    } else if (_data.key === 'one') {
      const jsonData = _.cloneDeep(oneJson)
      _.set(jsonData, 'assets[12].layers[2].t.d.k[0].s.t', `LV${_data.level}`)
      _.set(jsonData, 'assets[1].p', LEVEL_MEDALS_ICONS.default[_data.level])

      lottieRef.current?.loadLottie({
        key: _data.key,
        json: jsonData,
        loop: false,
      })
    } else if (_data.key === 'two') {
      const medalInfo = MEDAL_LIST.find((item) => item.key === _data.medal)!
      const jsonData = _.cloneDeep(twoJson)
      _.set(jsonData, 'assets[15].layers[2].t.d.k[0].s.t', `LV${_data.level}`)
      _.set(jsonData, 'assets[1].p', LEVEL_MEDALS_ICONS.default[_data.level])

      _.set(jsonData, 'assets[2].p', medalInfo.default_icon)
      _.set(jsonData, 'assets[14].layers[0].t.d.k[0].s.t', medalInfo.description)
      _.set(jsonData, 'assets[14].layers[1].t.d.k[0].s.t', medalInfo.name)

      lottieRef.current?.loadLottie({
        key: _data.key,
        json: jsonData,
        loop: false,
      })
    }
  }

  // 队列处理入口
  const onLottie = async (_data) => {
    // 将新任务添加到队列
    queueRef.current.push(_data)
    console.log('[lottie-home] 新任务添加到队列', _data)
    // 尝试处理队列
    await processNextInQueue()
  }

  useEffect(() => {
    EventBus.on(Events.onLottie, onLottie)

    return () => {
      EventBus.off(Events.onLottie, onLottie)
    }
  }, [])

  const onLoad = (_key) => {
  }

  const onError = (_message) => {

  }

  const onComplete = async (key) => {
    setPlay(false)
    resetElement(containRef.current)
    lottieRef.current?.destroyLottie({ key })

    // 标记当前任务完成
    processingRef.current = false

    // 处理队列中的下一个任务
    await processNextInQueue()
  }

  return (
    <div className={cn('lottie-home', { play })} ref={containRef}>
      <LottiePlayer
        ref={lottieRef}
        onLoad={onLoad}
        onError={onError}
        onComplete={onComplete}
      />
    </div>
  )
}

export default LottieHome
