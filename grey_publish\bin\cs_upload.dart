import 'dart:io';

import 'package:ppt101_cs_utils/cs_utils.dart';
import 'package:path/path.dart' as p;

CsContext csContext = CsContext(
  apiBaseUrl: "https://cs.101.com",
  accessKey: "qa3snOn0egY8HKNV",
  secretKey: "O629m7e55LMRhqIGHFlNOm2M8q0Zcb6d",
  serviceName: "aic_earth_research",
);

//dart cs_upload.dart --path=F:\Workspace\aic-earth-research\android\app\build\outputs\apk\release\app-release.apk --dir=apk
void main(List<String> args) async {
  print("main cmd: $args");

  String? path;
  String dir = "apk";
  for (var element in args) {
    if (element.startsWith('--path=')) {
      path = element.split('=')[1];
    } else if (element.startsWith('--dir=')) {
      dir = element.split('=')[1];
    }
  }
  if (path == null || path.isEmpty) {
    print("请指定上传的文件路径");
    return;
  }

  uploadLogToCS(path, dir);
}

/// 上传到cs并返回对应的dentryId
Future<String?> uploadLogToCS(String path, String dir) async {
  print("上传日志到CS:$path");
  CsClient client = CsClient(csContext);
  String fileName = p.basename(path);

  try {
    CsDentryModel model =
        await client.uploadFile(path, '/aic_earth_research/$dir/$fileName');
    File file = File(path);
    if (file.existsSync()) {
      file.deleteSync(recursive: true);
    }
    // String fileCSPath =
    //     'https://gcdncs.101.com/v0.1/download?dentryId=${model.dentryId}&serviceName=${csContext.serviceName}&attachment=true';
    // return fileCSPath;
    //https://gcdncs.101.com/v0.1/static/aic_earth_research/apk/app-release.apk
    print('upload log to cs success: ${model.dentryId}');
    return model.dentryId;
  } catch (e) {
    print('upload log to cs failed: $e');
    return null;
  }
}
