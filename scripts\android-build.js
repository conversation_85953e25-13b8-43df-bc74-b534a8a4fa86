#!/usr/bin/env node
/**
 * Android构建脚本
 * 支持环境参数和远程模式
 */

const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

// 解析npm参数
const parseArgs = () => {
    const config = {};

    // 从npm_config环境变量中获取参数
    if (process.env.npm_config_env) {
        config.env = process.env.npm_config_env;
    }
    if (process.env.npm_config_remote) {
        config.remote = process.env.npm_config_remote;
    }

    return config;
};

// 环境映射
const ENV_MAP = {
    test: 'test',
    pre: 'preproduction',
    preproduction: 'preproduction',
    prod: 'product',
    product: 'product',
    sdpsg: 'sdpsg'
};

const main = () => {
    const config = parseArgs();
    const env = ENV_MAP[config.env] || 'product';
    const isRemote = config.remote === 'true' || config.remote === true;
    const isRelease = process.argv.includes('--release') || process.argv[1].includes('release');

    console.log(`🚀 Android Build Configuration:`);
    console.log(`   Environment: ${env}`);
    console.log(`   Remote Mode: ${isRemote}`);
    console.log(`   Build Type: ${isRelease ? 'Release' : 'Debug'}`);
    console.log(''); try {
        // 第一步：构建Web应用 (远程模式下跳过，因为不需要本地文件)
        if (!isRemote) {
            console.log('📦 Step 1: Building Web Application...');
            const envVars = `cross-env SDP_ENV=${env}`;
            const buildCommand = `${envVars} npm run build`;
            console.log(`💻 Executing: ${buildCommand}`);
            execSync(buildCommand, { stdio: 'inherit', cwd: path.resolve(__dirname, '..') });
        } else {
            console.log('🌐 Step 1: Skipping Web build (Remote mode - using online URL)');
            // 在远程模式下，创建一个最小的dist目录以满足Capacitor要求
            const distPath = path.resolve(__dirname, '..', 'dist');
            if (!fs.existsSync(distPath)) {
                fs.mkdirSync(distPath, { recursive: true });
            }
            const indexPath = path.join(distPath, 'index.html');
            if (!fs.existsSync(indexPath)) {
                fs.writeFileSync(indexPath, '<!DOCTYPE html><html><head><title>Remote Mode</title></head><body><p>Remote mode - loading from URL</p></body></html>');
            }
        }

        // 第二步：选择正确的Capacitor配置并同步
        console.log('🔄 Step 2: Syncing with Capacitor...');
        if (isRemote) {
            // 复制远程配置文件
            const remoteConfig = path.resolve(__dirname, '..', 'capacitor.config.remote.ts');
            const originalConfig = path.resolve(__dirname, '..', 'capacitor.config.ts');

            // 备份原配置
            fs.copyFileSync(originalConfig, originalConfig + '.backup');
            // 使用远程配置
            fs.copyFileSync(remoteConfig, originalConfig);

            console.log(`🌐 Using remote configuration for environment: ${env}`);
        }

        const syncCommand = `cross-env SDP_ENV=${env} npx cap sync`;
        console.log(`💻 Executing: ${syncCommand}`);
        execSync(syncCommand, { stdio: 'inherit', cwd: path.resolve(__dirname, '..') });

        // 第三步：构建Android APK
        console.log('📱 Step 3: Building Android APK...');
        const gradleTask = isRelease ? 'assembleRelease' : 'assembleDebug';
        const androidCommand = `cd android && .\\gradlew ${gradleTask}`;
        console.log(`💻 Executing: ${androidCommand}`);
        execSync(androidCommand, { stdio: 'inherit', cwd: path.resolve(__dirname, '..'), shell: true });

        // 恢复原配置文件
        if (isRemote) {
            const originalConfig = path.resolve(__dirname, '..', 'capacitor.config.ts');
            fs.copyFileSync(originalConfig + '.backup', originalConfig);
            fs.unlinkSync(originalConfig + '.backup');
            console.log('🔄 Restored original configuration');
        }

        console.log('');
        console.log('✅ Android build completed successfully!');
        console.log(`📦 APK location: android/app/build/outputs/apk/${isRelease ? 'release' : 'debug'}/`);

    } catch (error) {
        // 恢复配置文件（如果出错）
        if (isRemote) {
            const originalConfig = path.resolve(__dirname, '..', 'capacitor.config.ts');
            if (fs.existsSync(originalConfig + '.backup')) {
                fs.copyFileSync(originalConfig + '.backup', originalConfig);
                fs.unlinkSync(originalConfig + '.backup');
            }
        }

        console.error('❌ Android build failed');
        console.error(error.message);
        process.exit(1);
    }
};

main();