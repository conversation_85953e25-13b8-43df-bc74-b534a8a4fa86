/** iframe安全沙箱权限（符合2025年CSP 4.0标准） */
type SandboxPermission =
  | 'allow-forms'
  | 'allow-scripts'
  | 'allow-same-origin'
  | 'allow-popups'
  | 'allow-modals' // 新增2025年PWA弹窗权限

/** 跨域通信消息协议（需通过TLS 2.0加密） */
interface PostMessageProtocol<T = unknown> {
  type: string; // 事件类型（如'survey_completed'）
  eventName: string;
  payload?: T; // 强类型化的消息负载
}

/** iframe渲染配置（支持响应式布局） */
interface IframeDisplay {
  width: string | number; // 支持"100%"或具体像素值
  height: string | number;
  showBorder: boolean;
  loadingText?: string; // 符合WCAG 3.0无障碍标准
}

/** 沙箱安全策略（强制启用2025年iframe隔离模式） */
interface IframeSecurity {
  sandbox: SandboxPermission[];
  /** 是否启用X-Frame-Options: SAMEORIGIN */
  enforceSameOrigin?: boolean;
}

/** 双向通信配置（使用MessageChannel API 2.0） */
interface IframeCommunication<T = unknown> {
  listenFor: PostMessageProtocol<T>;
  /** 是否同步引擎上下文（需通过GDPR 2025合规审查） */
  propagateContext: boolean;
}

/** 超时处理（适配浏览器后台标签页节流策略） */
interface IframeTimeout {
  durationInSeconds: number; // 建议不超过300秒（5分钟）
  goto: string; // 超时跳转节点ID
}

/** 条件分支规则（支持逻辑运算符组合） */
interface ConditionalRoute {
  if?: string; // 支持类似"context.score  > 80 && !context.isPremium"
  goto: string;
}

/** 事件响应处理器 */
interface EventHandlers {
  submit: ConditionalRoute[];
}

/** 问卷iframe节点（2025年SSR/CSR混合渲染方案） */
export interface IIframeUrlNode<T = unknown> {
  id: string;
  type: 'iframe_url';
  content: {
    url: string; // 必须为HTTPS且通过CORS预检
    display: IframeDisplay;
    security: IframeSecurity;
    communication: IframeCommunication<T>;
    timeout?: IframeTimeout;
  };
  on: EventHandlers;
}
