import { getQuery } from '@/util/uri'
import request from '@gem-mine/request'
import { buildUrl, paramsSerializer } from 'cloud-office-util/help'
import _ from 'lodash'
import { message } from 'fish'
import one from '../one'
import { config } from './proxy'

// 请求初始化
request.init(config, {
  // 开发模式下，如果某个域配置了 wds，则优先使用 wds 配置
  wds: process.env.NODE_ENV !== 'production',
  // 环境配置
  env: process.env.SDP_ENV
})

// 全局设置，对所有请求生效
request.config({
  timeout: 120000,
  paramsSerializer,
  before: async (_config) => {
    if (!_config.headers) {
      _config.headers = {}
    }

    const { uc } = one
    const { method = 'GET', params = {} } = _config
    let { url = '' } = _config
    url = buildUrl(url, params)
    const Authorization = await uc.getAuthHeaderAsync({ url, method })

    let headers: Record<string, string> = { Authorization }
    const { headers: customHeaders = {} } = _config
    headers = _.assign(headers, { 'sdp-biz-type': getQuery('sdp-biz-type') })
    headers = _.assign(headers, { 'sdp-app-id': getQuery('sdp-app-id') }, customHeaders)
    headers = _.pickBy(headers)

    return _.assign(_config, { headers })
  },
  error: ({ response }) => {
    const err = _.get(response, 'data.message', '')
    if (err) {
      message.error(err)
    }
    console.log('error', response)
    return Promise.reject(err || response)
  },
})
