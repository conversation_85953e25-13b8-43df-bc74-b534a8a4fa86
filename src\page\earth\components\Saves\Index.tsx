import React, {
  useEffect,
  useRef,
  useState,
} from 'react'
import classNames from 'classnames/bind'
import { useAppDispatch, useAppSelector } from '@/store'
import IndexedDbSDK from '@/sdk/indexedDbSDK'
import moment from 'moment'
import tipUser from '@/asset/images/saves/save_user.png'
import startBtn from '@/asset/images/saves/save_start.png'
import startText from '@/asset/images/saves/save_start_text.png'
import fullImage from '@/asset/images/saves/save_full_tips.png'
import titleImage from '@/asset/images/saves/save_title.png'
import deleteImage from '@/asset/images/saves/save_delete.png'
import continueImage from '@/asset/images/saves/save_continue.png'
import iconTitle from '@/asset/images/saves/icon_title.png'
import iconDelete from '@/asset/images/saves/icon_delete.png'
import cancelImage from '@/asset/images/saves/save_cancel.png'
import confirmImage from '@/asset/images/saves/save_confirm.png'
import guideMusic from '@/asset/musics/saves.wav'
import { useLocalStorageState } from 'ahooks'
import AudioManager from '@/util/AudioManager'
import { updateConfig } from '../../earthSlice'
import styles from './index.module.scss'

const cn = classNames.bind(styles)

const DEFAULT_SAVES = new Array(10).fill('').map((__, i) => ({ name: `存档${i + 1}` }))

const Saves = () => {
  const { config } = useAppSelector((state) => state.earth)
  const { saves, currentSaveId } = config
  const [showFull, setShowFull] = useState(false)
  const dispatch = useAppDispatch()
  const dbSDK = useRef<IndexedDbSDK>()
  const [confirmData, setConfirmData] = useState({
    saveId: '',
    visible: false,
  })
  const [firstUse, setFirstUseMessage] = useLocalStorageState<boolean>(
    'firstUse',
    {
      defaultValue: true,
    },
  )

  useEffect(() => {
    if (firstUse) {
      AudioManager.getInstance('sfx').play(guideMusic)
    }
    return () => {
      AudioManager.getInstance('sfx').stop()
    }
  }, [firstUse])

  const initSaves = async (type) => {
    const saveList = await dbSDK.current!.getAllSaves()
    const _list = DEFAULT_SAVES.map((item) => {
      const find = saveList.find((s) => s.name === item.name)
      if (find) {
        return find
      } else {
        return item
      }
    })
    dispatch(updateConfig({ saves: _list }))
    if (_list.every((s) => !s.id) && type === 'auto') {
      // 如果没有存档，则创建一个默认存档
      const firstEmptySave = _list.find((s) => !s.id)
      if (firstEmptySave) {
        // eslint-disable-next-line @typescript-eslint/no-use-before-define
        handleCreateSave(firstEmptySave.name)
      }
    }
  }

  // 加载存档
  const loadSave = async (saveId) => {
    if (currentSaveId === saveId) return
    await dbSDK.current!.loadSave(saveId)
    const save = saves.find((s) => s.id === saveId)
    if (save) {
      dispatch(updateConfig({ currentSaveId: saveId }))
    } else {
      console.error('存档不存在:', saveId)
    }
  }

  // 创建新存档
  const handleCreateSave = async (name) => {
    const saveId = await dbSDK.current!.createSave(name)
    initSaves('default')
    // 修改存档id触发main的监听加载
    dispatch(updateConfig({ currentSaveId: saveId }))
  }

  const cancelDelete = () => {
    setConfirmData({
      visible: false,
      saveId: ''
    })
  }

  const handleDeleteSave = async () => {
    await dbSDK.current!.deleteSave(confirmData.saveId)
    initSaves('default')
    if (currentSaveId === confirmData.saveId) {
      dispatch(updateConfig({ currentSaveId: '' }))
    }
    cancelDelete()
  }

  const confirmDelete = async (saveId) => {
    setConfirmData({
      visible: true,
      saveId
    })
  }

  const initDb = async () => dbSDK.current!.initPublicDB()

  useEffect(() => {
    dbSDK.current = IndexedDbSDK.getInstance()
    initDb().then(() => {
      initSaves('default')
    })
    return () => {
      setFirstUseMessage(false)
    }
  }, [])

  const onStart = () => {
    if (saves.every((s) => s.id)) {
      setShowFull(true)
      setTimeout(() => {
        setShowFull(false)
      }, 2000)
    } else {
      const firstEmptySave = saves.find((s) => !s.id)
      // 处理开始游戏逻辑
      handleCreateSave(firstEmptySave?.name)
    }
  }

  return (
    <div className={cn('saves')}>
      <div className={cn('saves-list')}>
        <div className={cn('list-header')}>
          <img src={titleImage} className={cn('header-image')} />
        </div>
        <div className={cn('list-content')}>
          {
            saves.map((item) => (
              !item.id ? <div key={item.name} className={cn('save-item')} />
                : (
                  <div key={item.name} className={cn('save-item', 'used')}>
                    <div className={cn('save-info')}>
                      <div className={cn('save-name')}>{item.name}</div>
                      <div className={cn('save-time')}>
                        <div className={cn('save-createdAt')}>
                          <span>
                            创建时间：
                            {moment(item.createdAt).format('YYYY-MM-DD HH:mm:ss')}
                          </span>
                        </div>
                        <div className={cn('save-lastPlayed')}>
                          <span>
                            修改时间：
                            {moment(item.lastPlayed).format('YYYY-MM-DD HH:mm:ss')}
                          </span>
                        </div>
                      </div>
                    </div>
                    <div className={cn('save-actions')}>
                      <img
                        className={cn('delete-save')}
                        onClick={() => {
                          confirmDelete(item.id)
                        }}
                        src={deleteImage}
                      />
                      <img
                        className={cn('load-save')}
                        onClick={() => {
                          if (item.id) {
                            loadSave(item.id)
                          } else {
                            handleCreateSave(item.name)
                          }
                        }}
                        src={continueImage}
                      />
                    </div>
                  </div>
                )
            ))
          }
        </div>
      </div>
      <div className={cn('save-tip')}>
        <img className={cn('save-tip-image')} src={tipUser} />
        <div className={cn('save-start')}>
          <img src={startText} className={cn('start-text')} />
          <img src={startBtn} className={cn('start-btn')} onClick={onStart} />
        </div>
      </div>
      <img src={fullImage} className={cn('full-image', { showFull })} />
      <div className={cn('delete-confirm', { visible: confirmData.visible })}>
        <div className={cn('delete-box')}>
          <div className={cn('header')}>
            <img src={iconTitle} />
            <img src={iconDelete} onClick={cancelDelete} />
          </div>
          <div className={cn('content')}>
            <div className={cn('text')}>
              删除存档后无法恢复，是否删除？
            </div>
            <div className={cn('buttons')}>
              <img src={cancelImage} onClick={cancelDelete} />
              <img src={confirmImage} onClick={handleDeleteSave} />
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Saves
