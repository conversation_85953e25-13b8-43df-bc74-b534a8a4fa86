// 消息结构与类型统一使用 MessageUtils
import { Message } from './MessageUtils'

export type MessageType = keyof typeof Message
export type IMessage = ReturnType<typeof Message[keyof typeof Message]>
type EventName = MessageType
type MessageData = any

// ====================== 核心通信 SDK ======================
class IframeCommunicationSDK {
  private iframe: HTMLIFrameElement | null

  private targetOrigin: string

  private messageHandlers: Map<EventName, Set<(data: MessageData, raw?: IMessage) => void>>

  private universalHandlers: Set<(data: MessageData, raw?: IMessage) => void> = new Set()

  /**
   * 构造函数
   * @param iframe - 目标iframe元素
   * @param targetOrigin - 允许通信的安全源（支持通配符*）
   */
  constructor(
    iframe: HTMLIFrameElement,
    targetOrigin: string = '*',
  ) {
    this.iframe = iframe
    this.targetOrigin = targetOrigin
    this.messageHandlers = new Map()
    // 安全绑定消息监听
    window.addEventListener('message', this.handleIncomingMessage)
  }

  // 消息处理核心
  private handleIncomingMessage = (event: MessageEvent): void => {
    const { data } = event
    // 只处理 MessageUtils 格式 { type, data }
    const keys = Object.keys(Message)
    if (typeof data === 'object' && keys.some((k) => k.toLowerCase() === data.type?.toLowerCase())) {
      console.log(`Received ${data.type} from iframe`, data)
      this.dispatchEvent(data.type as EventName, data.data, data)
    }
  }

  // 事件分发机制
  private dispatchEvent(eventName: EventName, payload: MessageData, raw?: IMessage): void {
    // 分发给特定类型的监听器
    const handlers = this.messageHandlers.get(eventName)
    if (handlers) {
      handlers.forEach((handler) => handler(payload, raw))
    }

    // 分发给通用监听器
    this.universalHandlers.forEach((handler) => handler(payload, raw))
  }

  /**
   * 向子iframe发送消息（统一 MessageUtils 格式）
   * @param type - 消息类型（参考 MessageUtils）
   * @param data - 消息内容
   */
  public sendToIframe(type: MessageType, data?: MessageData): void {
    if (!this.iframe?.contentWindow) {
      console.error('Iframe contentWindow not available')
      return
    }
    const msgObj = { type, data }
    this.iframe.contentWindow.postMessage(msgObj, this.targetOrigin)
    console.log(`Sent ${type} to iframe`, msgObj)
  }

  /**
   * 监听来自子iframe的消息（统一 util 格式）
   * @param type - 要监听的消息类型
   * @param handler - 回调处理函数 (data, raw)
   * @returns 取消监听函数
   */
  public listenFromIframe(
    type: MessageType,
    handler: (data: MessageData, raw?: IMessage) => void
  ): () => void {
    if (!this.messageHandlers.has(type)) {
      this.messageHandlers.set(type, new Set())
    }
    const handlers = this.messageHandlers.get(type)!
    handlers.add(handler)
    return () => {
      handlers.delete(handler)
      if (handlers.size === 0) {
        this.messageHandlers.delete(type)
      }
    }
  }

  /**
   * 监听所有消息（通用监听器）
   * @param handler - 回调处理函数 (data, raw)
   * @returns 取消监听函数
   */
  public listenAllMessages(
    handler: (data: MessageData, raw?: IMessage) => void
  ): () => void {
    this.universalHandlers.add(handler)
    return () => {
      this.universalHandlers.delete(handler)
    }
  }

  /**
   * 安全销毁通信实例
   */
  public destroy(): void {
    window.removeEventListener('message', this.handleIncomingMessage)
    this.messageHandlers.clear()
    this.universalHandlers.clear()
    this.iframe = null
    console.info('IframeCommunicationSDK  destroyed')
  }
}

export default IframeCommunicationSDK
