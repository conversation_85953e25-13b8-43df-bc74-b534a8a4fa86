.fingerTip {
  position: fixed;
  z-index: 99999;
  width: 100%;
  height: 100%;
  background: fixed rgba(0, 0, 0, 0.1);
  left: 0;
  top: 0;
  .fingerContent {
    position: absolute;
    .fingerImage {
      position: absolute;
      right: 0rem;
      width: 12.5rem;
      pointer-events: none;
      z-index: 2;
    }
    .fingerText {
      background: radial-gradient(
          134.74% 178.35% at 77.41% 0%,
          rgb(251 179 255 / 20%) 0%,
          rgba(251, 179, 255, 0) 100%
        ),
        linear-gradient(
          90deg,
          rgb(37 38 58 / 0%) 0%,
          #25263a 20%,
          #25263aa1 80%,
          rgb(37 38 58 / 0%) 100%
        );

      opacity: 0.9;
      padding: 0.3125rem 1.875rem;
      color: #fff;
      bottom: 0rem;
      white-space: nowrap;
      position: absolute;
      top: 4.375rem;
      z-index: 1;
      height: 2.125rem;
      left: unset;
      right: 5.625rem;
    }
    &.left {
      .fingerText {
        left: -7.5rem;
        right: unset;
      }
    }
    &.slide {
      .fingerText {
        left: -7.5rem;
        top: 3.75rem;
        right: unset;
      }
    }
  }
}
