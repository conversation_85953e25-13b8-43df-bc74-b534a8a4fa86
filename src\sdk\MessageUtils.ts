// Message 结构
// {
//   type: string; // 消息类型
//   data: any; // 消息内容
// }
// eslint-disable-next-line import/prefer-default-export
export const Message = {

  // =========================== http参数 =====================================
  // 放在地址栏上传递给3D
  // uid: string; // 用户唯一标识，用于保存数据时做用户隔离

  // =========================== 3D -> Web =====================================
  /**
   * 页面加载完成
   * 3D -> Web
   * type: "PageLoaded"
   */
  pageLoaded: () => ({ type: 'PageLoaded', data: {} }),

  /**
   * 收集线索
   * 3D -> Web
   * type: "CollectClue"
   * data: {
   *   key: string; // 线索的唯一标识
   *   title: string; // 线索标题
   *   description: string; // 线索描述
   *   imageUrl: string; // 线索图片URL
   * }
   *
   * 线索表格：
   * | key                    | title            | description                                              |
   * |------------------------|------------------|----------------------------------------------------------|
   * | clue_1                 | 火山岩浆         | 火山岩浆，高温熔融的岩石物质                             |
   * | clue_2                 | 地表裂缝         | 岩浆上涌产生巨大推力，让岩层断裂                         |
   * | clue_3                 | 岩石褶皱         | 是岩层受挤压后发生了弯曲变形，像被用力挤压的橡皮泥       |
   * | clue_4                 | 岩石断层         | 是岩层受力破裂并沿断裂面发生相对移动后的产物             |
   * | clue_5                 | 地震波的种类     | 横波，传播速度较慢，仅通过固体传播；纵波，传播速度快，能穿透固体、液体和气体。传播速度固体>液体>气体。 |
   * | clue_6                 | 地震波速度快速提升 | 在大陆70千米处，横波和纵波速度均开始快速提升，直至地下2900千米处 |
   * | clue_7                 | 地震波速度陡降或消失 | 在地下2900千米处，纵波传播速度陡降，横波传播速度急速下降直至消失 |
   * | clue_8                 | 纵波速度回升     | 在地下5150千米处纵波速度回升                             |
   * | clue_9                 | 石英石、云母     | 富含硅、铝元素                                           |
   * | clue_10                | 橄榄石           | 富含铁、镁元素                                           |
   * | clue_11                | 某种合金         | 富含大量铁元素、少量镍元素，跟陨石的成分很像             |
   */
  collectClue: (clue) => ({ type: 'CollectClue', data: { ...clue } }),

  /**
   * 完成收集线索
   * 3D -> Web
   * type: "CollectClueComplete"
   * data: {
   *   key: string; // 线索的唯一标识
   * }
   *
   * | key
   * |----------
   * | volcano
   * | mountain
   * | earthquake
   */
  collectClueComplete: (key: string) => ({ type: 'CollectClueComplete', data: { key } }),

  /**
    * 执行模拟，用于web统计模拟次数，触发AI辅助提醒
    * 3D -> Web
    * type: "DoSimulate"
    * data: {
    *  result: string // success:成功；fail_4:无裂解活跃；fail_5:地壳抑制；fail_7:薄地幔限制；fail_9:参数矛盾
    *  layer1: string // 最外层 material1（低温脆性岩石材质）；material2（超高温高压下的固态塑性岩石材质）；material3（超高温致密金属材质）
    *  layer2: string // 中间层
    *  layer3: string // 最内层
    *  layer1thickness:string // 最外层厚度 thin/thick
    *  layer2thickness:string // 中间层厚度 thin/thick
    *  layer3thickness:string // 最内层厚度 thin/thick
    *  movable:string // movable_0固定 / movable_1可移动
    *  crack:string // crack_0是 / crack_1否
    * }
    */
  doSimulate: (result: string) => ({ type: 'DoSimulate', data: { result } }),

  /**
   * 打开/关闭模拟设定面板回调
   * 3D -> Web
   * type: "OnSimulateSettingPanel"
   * data: {
   *  isOn: boolean; // 是否打开模拟设定面板
   * }
   */
  onSimulateSettingPanel: (isOn: boolean) => ({ type: 'OnSimulateSettingPanel', data: { isOn } }),

  /**
   * 前往下一个节点
   * 3D -> Web
   * type: "ToNext"
   */
  toNext() {
    return { type: 'ToNext', data: {} }
  },

  /**
   * 埋点
   * 3D -> Web
   * type: "TrackEvent"
   * data: {
   *  ...event // 埋点数据格式
   * }
   */
  trackEvent: (event: TrackEvent) => ({ type: 'TrackEvent', data: { ...event } }),

  /**
   * 显示新手引导
   * 3D -> Web
   * type: "ShowGuide"
   * data: {
   *  type: string; // 引导类型，'aimer'：瞄准镜引导；
   *  position?: [number, number]; // 位置
   * }
   */
  showGuide: (type: string, position?: [number, number]) => ({ type: 'ShowGuide', data: { type, position } }),

  // =========================== Web -> 3D =====================================
  /**
   * 打开地球模拟设定界面
   * Web -> 3D
   * type: "SimulateSettingPanel"
   * data: {
   *   isOn: boolean; // 是否打开地球模拟设定
   * }
   */
  simulateSettingPanel: (isOn: boolean) => ({ type: 'SimulateSettingPanel', data: { isOn } }),

  /**
   * 我的假设的数据
   * Web -> 3D
   * type: "AssumptionDatas"
   * data: {
   *   ...data // 假设数据格式
   * }
   */
  assumptionDatas: (data: any) => ({ type: 'AssumptionDatas', data: { ...data } }),

  // =========================== Web <-> 3D =====================================
  /**
   * 关键表演
   * 3D <-> Web
   * type: "OnKeyPerformance"
   * data: {
   *  key: string; // 关键表演
   * }
   *
   * | key                          | 关键表演                    | 触发方
   * |------------------------------|----------------------------|-----------
   * | onEarthquakeStartChat        | 可以地震场景开始NPC对话      | 3D -> Web
   * | onEarthquakeShowButton       | 可以显示地震波采集按钮       | Web -> 3D
   * | onEarthquakeCollectWaveData  | 完成采集地震波数据           | 3D -> Web
   * | onEarthquakeShowChart        | 可以显示地震波图表           | Web -> 3D
   * | onEarthquakeShowStartCollect | 完成NPC对话，显示收集线索按钮 | Web -> 3D
   * | onEarthquakePressStartCollect | 按下开始收集按钮            | 3D -> Web
   */
  onKeyPerformance: (key: string) => ({ type: 'OnKeyPerformance', data: { key } }),
}
