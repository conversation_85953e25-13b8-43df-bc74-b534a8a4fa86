.hypothesis {
  height: 100%;
  display: flex;

  // 动画定义
  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(1.25rem);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .sources {
    flex: 1;
    overflow: hidden;
    background: transparent;
    .source-title-box {
      margin: 2.1875rem 0rem 0rem 1.875rem;
      .source-title {
        font-family: "Alibaba PuHuiTi";
        font-size: 1.5rem;
        font-style: normal;
        font-weight: 700;
        color: #655c5c;
        margin-right: 2.75rem;
        position: relative;
        text-shadow: 0 0.1875rem 0 #fff;
        .source-key {
          position: absolute;
          left: 0rem;
          top: 1.875rem;
          width: 3.75rem;
        }
      }
      .source-des {
        color: #b5a48b;
        text-align: right;
        font-family: "Alibaba PuHuiTi";
        font-size: 0.875rem;
        font-style: normal;
        font-weight: 400;
        text-transform: uppercase;
        position: relative;
        img {
          position: absolute;
          top: 0.625rem;
          right: 1.25rem;
          width: 100%;
        }
      }
    }
    .source-list {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      grid-template-rows: repeat(3, 1fr);
      gap: 0.625rem;
      padding: 1.25rem 1.875rem;
      height: 33.75rem; // 固定高度以适应3行布局
      overflow: hidden;
      position: relative;
      transition: opacity 0.3s ease;
      user-select: none; // 防止选中文本

      .source-item {
        width: 12.5rem;
        height: 10rem;
        text-align: center;
        background: url("../../../../../../../asset/images/books/hypothesis/source_imgs/clue_photo_on.png")
          no-repeat center center / contain;
        cursor: pointer;
        transition: transform 0.2s ease;
        opacity: 0;
        animation: fadeInUp 0.3s ease forwards;
        &.dragging {
          opacity: 0 !important;
        }
        @for $i from 1 through 6 {
          &:nth-child(#{$i}) {
            animation-delay: #{($i - 1) * 0.1}s;
          }
        }

        &:hover {
          transform: scale(1.05);
        }

        img {
          width: 11.25rem;
          height: 6.25rem;
          margin: 0.875rem 0rem 0rem 0rem;
          pointer-events: none; // 防止拖拽图片
        }

        .source-des {
          width: 12.5rem;
          height: 1.875rem;
          margin: 0rem auto;
          font-size: 0.75rem;
          color: #666;
          margin-top: 0.3125rem;
          word-break: break-word;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          padding: 0rem 1.5rem;
        }
      }
    }

    .page-indicators {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 0.5rem;
      margin-top: 1.25rem;
      padding-bottom: 1.25rem;

      .page-dot {
        width: 0.6875rem;
        height: 0.6875rem;
        cursor: pointer;
        transition: all 0.3s ease;

        &.active {
          background-color: #655c5c;
          width: 0.75rem;
          height: 0.75rem;
        }
      }
    }
  }
  .target {
    flex: 1;
    background: transparent;
    padding-left: 2.5rem;
    padding-top: 1.25rem;
    overflow: hidden;
    position: relative;
    .custom-tabs {
      height: 100%;
    }
    .tab-header {
      display: flex;
      height: 3.75rem;
      align-items: center;
      position: relative;
      img {
        position: absolute;
        width: 100%;
        bottom: 0.75rem;
      }
      .tab-list {
        flex: 1;
        display: flex;
        overflow-x: auto;
        min-width: 0;
        white-space: nowrap;
        &::-webkit-scrollbar {
          display: none;
        }
        -ms-overflow-style: none;
        scrollbar-width: none;

        .tab-item {
          height: 2rem;
          line-height: 2rem;
          width: 8.75rem;
          flex-shrink: 0;
          color: #fff;
          font-family: "Alibaba PuHuiTi";
          font-size: 0.875rem;
          font-style: normal;
          font-weight: 500;
          text-align: center;
          position: relative;
          background: url("../../../../../../../asset/images/books/hypothesis/target_imgs/tab_c_off.png")
            no-repeat center center / contain;

          &.first {
            background: url("../../../../../../../asset/images/books/hypothesis/target_imgs/tab_l_off.png")
              no-repeat center center / contain;
            &.active {
              background: url("../../../../../../../asset/images/books/hypothesis/target_imgs/tab_l_on.png")
                no-repeat center center / contain;
            }
          }
          &.active {
            background: url("../../../../../../../asset/images/books/hypothesis/target_imgs/tab_c_on.png")
              no-repeat center center / contain;
            z-index: 99 !important;
          }
          &.one {
            width: 8.75rem;
          }
        }
      }
      .tab-extra {
        margin-left: 1.25rem;
        width: 3.125rem;
        height: 2rem;
        background: url("../../../../../../../asset/images/books/hypothesis/target_imgs/tab_add.png")
          no-repeat center center / contain;
      }
    }
    .tab-content {
      height: calc(100% - 3.75rem);
      .active {
        height: 100%;
      }
    }
    .hy-form-content {
      display: flex;
      height: 100%;
      flex-direction: column;
      label {
        color: #664b48;
        font-size: 1rem;
        font-weight: 700;
      }
      :global {
        .fish-form-item-label {
          text-align: center;
        }
        .fish-input-wrapper {
          border: 0.0625rem solid #9d8c6d;
          box-shadow: 0.125rem 0.125rem 0 0 rgba(132, 118, 116, 0.25) inset;
        }
        .fish-row {
          margin-bottom: 0;
        }
        .fish-input-suffix {
          right: 0.3125rem;
          img:first-child {
            height: 1.5rem;
          }
          img:last-child {
            height: 2rem;
            width: 2rem;
          }
        }
        .fish-input-lg {
          height: 2.5rem;
          font-size: 1rem;
          padding: 0.357rem 0.6875rem;
        }
        .fish-input {
          height: 2rem;
          font-size: 1rem;
          padding: 0.25rem 0.6875rem;
        }
      }
      .form-qp {
        flex: 1;
        overflow: auto;
        .form-group {
          background: #e0d6cc;
          border-radius: 0.625rem;
          margin-bottom: 0.8125rem;
          padding: 1.125rem 1.25rem;
        }
      }
      &.form-p {
        padding: 1.125rem 1.25rem;
        background: #e0d6cc;
        border-radius: 0.625rem;
        .form-title {
          color: #847674;
          font-size: 1rem;
          margin-bottom: 0.625rem;
          display: flex;
          align-items: center;
          justify-content: space-between;
          img {
            width: 1.875rem;
            height: 1.875rem;
            cursor: pointer;
          }
        }
        :global {
          .fish-row {
            margin-bottom: 0rem;
          }
          .fish-divider {
            margin: 0.625rem 0;
          }
        }
      }

      .relation-box {
        display: flex;
        gap: 0.625rem;
        height: 5.625rem;
        padding-top: 0.625rem;
        overflow: hidden;
        overflow-x: auto;
        &::-webkit-scrollbar {
          height: 0.125rem;
        }
        .relation-clue {
          height: 100%;
          text-align: center;
          width: 100%;
          min-width: 7.8125rem;
          position: relative;
          .relation-delete-icon {
            width: 1.25rem;
            height: 1.25rem;
            right: -0.3125rem;
            top: -0.625rem;
            cursor: pointer;
            position: absolute;
          }
          img {
            width: 100%;
            height: 100%;
          }
        }
      }
      .form-groups-btns {
        height: 3.75rem;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
        div {
          width: 11.25rem;
          background: #f2e8d8;
          border-radius: 0.625rem;
          cursor: pointer;
          height: 2.5rem;
          line-height: 2.5rem;
          transition: all 0.3s ease;
          img {
            width: 1.875rem;
            height: 1.875rem;
            position: relative;
            left: -0.3125rem;
            top: -0.125rem;
          }
          &.confirm-btn {
            background: #d4b787;
          }
        }
        div + div {
          margin-left: 1.25rem;
        }
      }

      .form-group-wrapper {
        position: relative;
        &.delete-mode {
          .form-group {
            opacity: 0.8;
            .fish-input {
              background: #f5f5f5;
            }
          }
        }
        .delete-icon {
          position: absolute;
          top: 0.625rem;
          right: 0.625rem;
          z-index: 10;
          cursor: pointer;
          width: 1.875rem;
          height: 1.875rem;
        }
      }
    }
  }
}

.overlay-wrap {
  i {
    display: none;
  }
  :global {
    .fish-overlay {
      background: linear-gradient(0deg, rgba(0, 0, 0, 0.64) 0%, rgba(0, 0, 0, 0.64) 100%),
        linear-gradient(69deg, rgba(236, 122, 249, 0.2) 4.17%, rgba(170, 108, 254, 0) 36.19%);
      backdrop-filter: blur(10px);
    }
  }
  .overlay-item {
    .title {
      overflow: hidden;
      color: #fff;
      text-align: center;
      text-overflow: ellipsis;
      text-shadow: 0 0.25rem 0.25rem rgba(0, 0, 0, 0.25);
      font-family: "Alibaba PuHuiTi";
      font-size: 1.125rem;
      font-style: normal;
      font-weight: 700;
      line-height: 3.375rem; /* 150% */
    }
    .close {
      position: absolute;
      right: 1.125rem;
      width: 2.5rem;
      height: 2.5rem;
      top: 1.125rem;
    }
    .content {
      position: absolute;
      top: 50%;
      left: 50%;
      width: 27.5rem;
      transform: translate(-50%, -50%);
      .img-bg {
        background: url("../../../../../../../asset/images/books/hypothesis/source_imgs/clue_edit_img.png")
          no-repeat center center/ cover;
        .img {
          width: calc(100% - 1.25rem);
          margin: 0.625rem;
        }
        margin-bottom: 1.25rem;
      }
      .textarea {
        border: 0rem !important;
        box-shadow: unset !important;
      }
    }
  }
}

.wheel {
  height: 96%;
  width: calc(50% - 3.125rem);
  position: fixed;
  bottom: 0;
  background: url("../../../../../../../asset/images//books/hypothesis/wheel/bg.png") no-repeat
    center center;
  background-size: 100% 100%;
  padding: 0.625rem 2.5rem;
  left: 50%;
  z-index: 100;
  .close {
    position: absolute;
    right: 0rem;
    top: -1rem;
    width: 2.5rem;
    height: 2.5rem;
  }
  .header {
    font-size: 1.125rem;
    color: #fff;
    position: relative;
    margin-bottom: 1.25rem;
    background: url("../../../../../../../asset/images/books/hypothesis/wheel/divergence_title.png")
      no-repeat center center / cover;
    text-align: center;
    height: 5.625rem;
    line-height: 5.625rem;
  }
  .relation {
    background: url("../../../../../../../asset/images/books/hypothesis/wheel/diverge_clues_bg.png")
      no-repeat center center / cover;
    padding: 1.25rem;
    .clue-box {
      display: flex;
      .relation-clue-text {
        flex: 1;
        line-height: 8.375rem;
        padding-left: 0.625rem;
        color: #000;
        font-weight: 500;
        color: #fff;
        font-family: "Alibaba PuHuiTi";
        font-size: 1rem;
        font-weight: 500;
        background: url("../../../../../../../asset/images/books/hypothesis/wheel/divergence_clue_text_box.png")
          no-repeat center center / contain;
      }
    }
  }
  .relation-clue {
    width: 12.5rem;
    height: 8.375rem;
    position: relative;
    padding: 0.75rem 0rem;
    i {
      font-size: 2.5rem;
      line-height: 5rem;
      left: 40%;
      position: absolute;
    }
    img {
      width: 100%;
      height: 100%;
    }
  }
  .question {
    margin-top: 0.625rem;
    display: flex;
    :global {
      .fish-input-wrapper {
        border: 0.0625rem solid #9d8c6d;
        box-shadow: 0.125rem 0.125rem 0 0 rgba(132, 118, 116, 0.25) inset;
      }
    }
    .question-submit {
      margin-left: 0.625rem;
      width: 6.25rem;
      background: url("../../../../../../../asset/images/books/hypothesis/wheel/issue_save_button_normal.png")
        no-repeat center center / contain;
      &:active {
        background: url("../../../../../../../asset/images/books/hypothesis/wheel/issue_save_button_press.png")
          no-repeat center center / contain;
      }
    }
  }
  .luck-index {
    z-index: 2;
    background: url("../../../../../../../asset/images/books/hypothesis/wheel/question_tips.png")
      no-repeat center center / cover;
    width: 100%;
    color: #494692;
    line-height: 1.5rem;
    text-align: center;
    letter-spacing: 0.09375rem;
    font-family: "Alibaba PuHuiTi";
    font-size: 1rem;
  }
  .luck {
    width: calc(100% - 1.25rem);
    height: 19.375rem;
    overflow: hidden;
    position: relative;
    img {
      position: absolute;
      top: 3.125rem;
      left: 0;
      width: 100%;
    }
    .turn {
      scale: 0.75;
      left: -0.25rem;
      top: 2.825rem;
    }
    .pointer {
      width: 6.6rem;
      left: 9.8rem;
      top: 10.2rem;
      &.on {
        width: 7.3rem;
        left: 9.7rem;
        top: 10.2rem;
      }
    }
  }
}

.custom-popconfirm {
  :global {
    .fish-popover-content,
    .fish-popover-inner {
      border-radius: 0.625rem;
    }
    .fish-popover-arrow {
      border-color: #f2e8d8 !important;
      background-color: #f2e8d8;
    }
    .fish-popover-inner-content {
      border: 0.3125rem solid #f2e8d8 !important;
      border-radius: 0.625rem;
      padding: 1.5rem 2rem;
    }
    .fish-popover-message-title {
      letter-spacing: 0.125rem;
      color: #664b48;
      text-align: center;
      font-family: "Alibaba PuHuiTi";
      font-size: 1.125rem;
      font-style: normal;
      font-weight: 500;
    }
    .fish-popover-buttons {
      text-align: center;
      margin-top: 1.25rem;
      button {
        height: auto;
        padding: 0.4375rem 1.5rem;
        font-size: 1rem;
        border-radius: 0.25rem;
        + button {
          margin-left: 1.25rem;
        }
      }
      .fish-btn {
        background-color: #f2e8d8;
        box-shadow: 0 0.125rem 0 rgba(158, 130, 128, 0.4);
      }
      .fish-btn-primary {
        border: 0rem;
        color: #000;
        background: #d4b787;
      }
    }
  }
}
