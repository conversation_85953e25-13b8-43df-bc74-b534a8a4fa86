// 其他勋章
import curious_rocket_default from '@/asset/images/achievement/other_medals/curious_rocket_default.png'
import curious_rocket_disabled from '@/asset/images/achievement/other_medals/curious_rocket_disabled.png'
import explorer_master_default from '@/asset/images/achievement/other_medals/explorer_master_default.png'
import explorer_master_disabled from '@/asset/images/achievement/other_medals/explorer_master_disabled.png'
import deep_explorer_master_default from '@/asset/images/achievement/other_medals/deep_explorer_master_default.png'
import deep_explorer_master_disabled from '@/asset/images/achievement/other_medals/deep_explorer_master_disabled.png'
import perseverance_default from '@/asset/images/achievement/other_medals/perseverance_default.png'
import perseverance_disabled from '@/asset/images/achievement/other_medals/perseverance_disabled.png'
import ai_communication_pioneer_default from '@/asset/images/achievement/other_medals/ai_communication_pioneer_default.png'
import ai_communication_pioneer_disabled from '@/asset/images/achievement/other_medals/ai_communication_pioneer_disabled.png'

// 等级勋章
import level_default from '@/asset/images/achievement/level_des/default.png'
import level_1 from '@/asset/images/achievement/level_des/LV1.png'
import level_2 from '@/asset/images/achievement/level_des/LV2.png'
import level_3 from '@/asset/images/achievement/level_des/LV3.png'
import level_4 from '@/asset/images/achievement/level_des/LV4.png'
import level_5 from '@/asset/images/achievement/level_des/LV5.png'
import level_6 from '@/asset/images/achievement/level_des/LV6.png'
import level_7 from '@/asset/images/achievement/level_des/LV7.png'

// 等级勋章图标
import lv1_default from '@/asset/images/achievement/level_medals/lv1_default.png'
import lv2_default from '@/asset/images/achievement/level_medals/lv2_default.png'
import lv3_default from '@/asset/images/achievement/level_medals/lv3_default.png'
import lv4_default from '@/asset/images/achievement/level_medals/lv4_default.png'
import lv5_default from '@/asset/images/achievement/level_medals/lv5_default.png'
import lv6_default from '@/asset/images/achievement/level_medals/lv6_default.png'
import lv7_default from '@/asset/images/achievement/level_medals/lv7_default.png'
import lv1_disabled from '@/asset/images/achievement/level_medals/lv1_disabled.png'
import lv2_disabled from '@/asset/images/achievement/level_medals/lv2_disabled.png'
import lv3_disabled from '@/asset/images/achievement/level_medals/lv3_disabled.png'
import lv4_disabled from '@/asset/images/achievement/level_medals/lv4_disabled.png'
import lv5_disabled from '@/asset/images/achievement/level_medals/lv5_disabled.png'
import lv6_disabled from '@/asset/images/achievement/level_medals/lv6_disabled.png'
import lv7_disabled from '@/asset/images/achievement/level_medals/lv7_disabled.png'

export const SCORE_LIST = [
  {
    name: '收集线索',
    key: 'clue',
    count: 0,
    description: '收集一个线索获得1积分'
  },
  {
    name: '提问',
    key: 'question',
    count: 0,
    description: '提出一个问题获得1积分'
  },
  {
    name: '假设1.0',
    key: 'question_presumes',
    count: 0,
    description: '提出一个假设获得1积分'
  },
  {
    name: '迭代假设',
    key: 'presumes',
    count: 0,
    description: '新增一个假设获得1积分'
  },
  {
    name: '地球模拟实验',
    key: 'experiment',
    count: 0,
    description: '每次模拟获得1积分'
  }
]

export enum MedalKey {
  earth_researcher = 'earth_researcher',
  curious_rocket = 'curious_rocket',
  explorer = 'explorer',
  deep_explorer = 'deep_explorer',
  perseverance = 'perseverance',
  communication_pioneer = 'communication_pioneer'
}

export const LEVEL_MEDALS_ICONS = {
  disabled: [
    lv1_disabled,
    lv2_disabled,
    lv3_disabled,
    lv4_disabled,
    lv5_disabled,
    lv6_disabled,
    lv7_disabled
  ],
  default: [
    lv1_default,
    lv2_default,
    lv3_default,
    lv4_default,
    lv5_default,
    lv6_default,
    lv7_default
  ]
}
export const LEVEL_MEDALS = [
  level_default,
  level_1,
  level_2,
  level_3,
  level_4,
  level_5,
  level_6,
  level_7,
]

export const MEDAL_LIST = [
  {
    name: '地球小研究员',
    key: MedalKey.earth_researcher,
    dis_icon: level_default,
    default_icon: level_1, // 实际显示根据等级来
    description: '完成线索收集、提出问题、生成假设、迭代假设、模拟实验、实验结论、实验反思中任意3项任务即可获得。'
  },
  {
    name: '好奇小火箭',
    key: MedalKey.curious_rocket,
    dis_icon: curious_rocket_disabled,
    default_icon: curious_rocket_default,
    description: '提问环节，\n获得超过3个（不含）“地球研究积分”'
  },
  {
    name: '探索达人',
    key: MedalKey.explorer,
    dis_icon: explorer_master_disabled,
    default_icon: explorer_master_default,
    description: '生成假设环节，\n获得超过3个（不含）“地球研究积分”'
  },
  {
    name: '深度探索达人',
    key: MedalKey.deep_explorer,
    dis_icon: deep_explorer_master_disabled,
    default_icon: deep_explorer_master_default,
    description: '迭代假设环节，\n获得超过3个（不含）“地球研究积分”'
  },
  {
    name: '锲而不舍',
    key: MedalKey.perseverance,
    dis_icon: perseverance_disabled,
    default_icon: perseverance_default,
    description: '地球模拟实验环节，\n获得超过3个“地球研究积分”'
  },
  {
    name: '智能交流先锋',
    key: MedalKey.communication_pioneer,
    dis_icon: ai_communication_pioneer_disabled,
    default_icon: ai_communication_pioneer_default,
    description: '实验全程，\n主动唤起AI超过3次'
  }
]
