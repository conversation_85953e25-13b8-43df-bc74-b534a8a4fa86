import React, {
  FC, useRef, useCallback
} from 'react'
import arrowAssistant from '../images/assistant_message_arrow.png'
import arrowUser from '../images/user_message_arrow.png'
import bgAssistant from '../images/assistant_message_bg.png'
import bgUser from '../images/user_message_bg.png'
import cameraPng from '../images/message_item_camera.png'
import dislikePng from '../images/message_item_dislike.png'
import likePng from '../images/message_item_like.png'
import resetPng from '../images/message_item_reset.png'
import voicePng from '../images/message_item_voice.png'
import downPng from '../images/message_item_down.png'

import { IMessageItemProps } from '../interface'
import styles from './MessageItem.module.scss'

const MessageItem: FC<IMessageItemProps> = ({
  message,
  userAvatar,
  avatar,
  onLike,
  onScreenshot,
  onVoice,
  onRegenerate,
  enableScreenshot = true,
  enableLike = true,
  enableVoice = true,
  enableRegenerate = true,
  hideOperation = false
}) => {
  const messageRef = useRef<HTMLDivElement>(null)

  const isUser = message.role === 'user'
  const isAssistant = message.role === 'assistant'
  // const isLiked = message.meta?.liked ?? false

  const handleLike = useCallback(async () => {
    if (!enableLike || onLike === undefined) return

    onLike(message.id)
  }, [enableLike, onLike, message.id])

  const handleScreenshot = useCallback(async () => {
    if (!enableScreenshot || onScreenshot === undefined || messageRef.current === null) return
    onScreenshot(message.id)
  }, [enableScreenshot, onScreenshot, message.id])

  const handleVoice = useCallback(() => {
    if (!enableVoice || onVoice === undefined) return
    onVoice(message.id)
  }, [enableVoice, onVoice, message.id])

  const handleRegenerate = useCallback(() => {
    if (!enableRegenerate || onRegenerate === undefined) return
    onRegenerate(message.id)
  }, [enableRegenerate, onRegenerate, message.id])

  // 渲染头像
  const renderAvatar = () => {
    if (isUser) {
      return userAvatar ? (
        <img src={userAvatar} alt="用户头像" className={styles.avatarImg} />
      ) : (
        <div className={styles.defaultAvatar}>
          👤
        </div>
      )
    } else {
      return avatar ? (
        <img src={avatar} alt="AI头像" className={styles.avatarImg} />
      ) : (
        <div className={styles.defaultAvatar}>
          🤖
        </div>
      )
    }
  }

  return (
    <div
      ref={messageRef}
      className={`${styles.messageItem} ${isUser ? styles.userMessage : styles.assistantMessage}`}
    >
      {/* 头像 */}
      <div className={styles.messageAvatar}>
        {renderAvatar()}
      </div>

      {isAssistant && (
        <div className={styles.messageContentBox}>
          <div className={styles.messageName}>
            馆长
          </div>
          <div className={styles.messageContent}>
            <img src={arrowAssistant} className={styles.arrowIcon} />
            <img src={bgAssistant} className={styles.bgIcon} />
            <div className={styles.status}>
              {
                message.status === 'loading' && <span>思考中...</span>
              }
              {
                message.status === 'error' && <span>出错了</span>
              }
              {
                message.status === 'completed' && <span>已完成思考</span>
              }
              <img src={downPng} className={styles.downIcon} />
            </div>
            <div className={styles.messageText}>
              {message.content}
            </div>
            {!hideOperation && (
              <div className={styles.messageActions}>
                {enableScreenshot && (
                  <img
                    src={cameraPng}
                    className={`${styles.actionBtn}`}
                    onClick={handleScreenshot}
                  />
                )}
                {enableLike && (
                  <>
                    <img
                      src={likePng}
                      className={`${styles.actionBtn} ${styles.liked}`}
                      onClick={handleLike}
                    />
                    <img
                      src={dislikePng}
                      className={`${styles.actionBtn}`}
                      onClick={handleLike}
                    />
                  </>
                )}
                {enableVoice && (
                  <img
                    className={styles.actionBtn}
                    onClick={handleVoice}
                    src={voicePng}
                  />
                )}

                {enableRegenerate && (
                  <img
                    src={resetPng}
                    className={`${styles.actionBtn} ${styles.reset}`}
                    onClick={handleRegenerate}
                  />
                )}
              </div>
            )}
          </div>
        </div>
      )}
      {
        isUser && (
          <div className={styles.messageContent}>
            <img src={arrowUser} className={styles.arrowIcon} />
            <img src={bgUser} className={styles.bgIcon} />
            <div className={styles.messageText}>
              {message.content}
            </div>
          </div>
        )
      }
    </div>
  )
}

export default MessageItem
