/**
 * 音频服务类 - 管理音频加载、播放和控制
 */
class AudioService {
  // 存储所有音频实例的映射表
  private static audioInstances: Map<string, HTMLAudioElement> = new Map()

  /**
   * 加载音频资源
   * @param param0 - 包含key(音频标识)和url(音频URL)的对象
   */
  public static audioLoad({ key, url }: { key: string; url: string }): void {
    // 如果已存在相同key的音频，先移除旧实例
    this.audioStop({ key })

    const audio = new Audio(url)
    audio.preload = 'auto'
    audio.onerror = (e) => console.error(`[AudioService]  音频加载失败 ${key}`, url, e)

    this.audioInstances.set(key, audio)
    console.log(`[AudioService]  加载音频 ${key}`, url)
  }

  /**
   * 设置音频音量
   * @param param0 - 包含key(音频标识)和volume(0-1音量值)的对象
   */
  public static audioVolume({ key, volume }: { key: string; volume: number }): void {
    const audio = this.audioInstances.get(key)
    if (audio) {
      audio.volume = Math.max(0, Math.min(1, volume))
      console.log(`[AudioService]  设置音量 ${key}:`, volume)
    }
  }

  /**
   * 播放音频
   * @param param0 - 包含key(音频标识)的对象
   */
  public static audioPlay({ key }: { key: string }): void {
    const audio = this.audioInstances.get(key)
    if (audio) {
      audio.play().catch((e) => {
        console.error(`[AudioService]  播放失败 ${key}:`, e.message)
      })
      console.log(`[AudioService]  播放音频 ${key}`)
    }
  }

  /**
   * 暂停音频
   * @param param0 - 包含key(音频标识)的对象
   */
  public static audioPause({ key }: { key: string }): void {
    const audio = this.audioInstances.get(key)
    if (audio && !audio.paused) {
      audio.pause()
      console.log(`[AudioService]  暂停音频 ${key}`)
    }
  }

  /**
   * 停止音频并重置进度
   * @param param0 - 包含key(音频标识)的对象
   */
  public static audioStop({ key }: { key: string }): void {
    const audio = this.audioInstances.get(key)
    if (audio) {
      audio.pause()
      audio.currentTime = 0
      console.log(`[AudioService]  停止音频 ${key}`)

      // 移除音频实例
      this.audioInstances.delete(key)
    }
  }

  /**
   * 跳转到指定播放位置
   * @param param0 - 包含key(音频标识)和time(时间/秒)的对象
   */
  public static audioSeek({ key, time }: { key: string; time: number }): void {
    const audio = this.audioInstances.get(key)
    if (audio) {
      audio.currentTime = time
      console.log(`[AudioService]  跳转音频 ${key} 到 ${time}s`)
    }
  }

  /**
   * 清理所有音频资源
   */
  public static cleanup(): void {
    this.audioInstances.forEach((audio) => {
      audio.pause()
      audio.src = ''
    })
    this.audioInstances.clear()
    console.log('[AudioService]  清理所有音频资源')
  }
}

// 初始化清理监听（页面卸载时清理资源）
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => AudioService.cleanup())
}

export default AudioService
