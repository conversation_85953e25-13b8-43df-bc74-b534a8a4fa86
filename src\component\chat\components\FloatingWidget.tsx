import React, {
  FC, useRef, useMemo
} from 'react'
import { useSize } from 'ahooks'
import Draggable from 'react-draggable'
import arrowAssistant from '../images/assistant_message_arrow.png'
import bgAssistant from '../images/assistant_message_bg.png'
import { IFloatingWidgetProps } from '../interface'
import styles from './FloatingWidget.module.scss'

const FloatingWidget: FC<IFloatingWidgetProps> = ({
  visible,
  position,
  avatar,
  onPositionChange,
  onToggleChat,
  onClick,
  notificationContent,
}) => {
  if (!visible) return null

  const isDragging = useRef(false)
  const dragStartTime = useRef(0)
  const avatarRef = useRef<HTMLDivElement>(null)

  // 使用ahooks获取屏幕尺寸
  const windowSize = useSize(() => document.querySelector('body'))

  // 判断FloatingWidget在屏幕的左边还是右边
  const isOnRightSide = useMemo(() => {
    if (!windowSize?.width) return false

    const screenWidth = windowSize.width
    const widgetCenterX = position.x + 60 // FloatingWidget宽度的一半 (120px / 2 = 60px)
    const screenCenterX = screenWidth / 2

    return widgetCenterX > screenCenterX
  }, [position.x, windowSize?.width])

  const handleStart = (e: any): void => {
    // 阻止默认行为，防止拖拽时显示禁用图标
    e.preventDefault()
    isDragging.current = false
    dragStartTime.current = Date.now()
  }

  const handleDrag = (): void => {
    // 拖拽过程中阻止默认行为
    isDragging.current = true
  }

  const handleStop = (e: any, data: any): void => {
    onPositionChange({ x: data.x, y: data.y })
    // 短暂延迟后重置拖拽状态，避免点击事件被误触发
    setTimeout(() => {
      isDragging.current = false
    }, 100)
  }

  const handleClick = (): void => {
    // 只有在没有拖拽的情况下才触发点击事件
    // 并且拖拽时间很短（小于200ms）时才认为是点击
    const dragDuration = Date.now() - dragStartTime.current
    if (!isDragging.current && dragDuration < 200) {
      onClick?.()
      onToggleChat()
    }
  }

  return (
    <Draggable
      position={position}
      onStart={handleStart}
      onDrag={handleDrag}
      onStop={handleStop}
      handle=".drag-handle"
      bounds="#root" // 限制在整个页面内拖拽
      cancel=""
      defaultClassName="react-draggable"
      defaultClassNameDragging="react-draggable-dragging"
      defaultClassNameDragged="react-draggable-dragged"
    >
      <div className={styles.floatingWidget}>
        <div
          ref={avatarRef}
          className={`${styles.avatar} drag-handle`}
          onClick={handleClick}
          onTouchEnd={handleClick}
          title="点击打开AI助手"
          onDragStart={(e) => e.preventDefault()}
          id="floating-widget-avatar"
        >
          {avatar !== undefined && avatar !== ''
            ? (
              <img src={avatar} alt="AI助手" className={styles.avatarImg} />
            )
            : (
              <div className={styles.defaultAvatar}>
                AI
              </div>
            )}
        </div>
        {notificationContent && (
          <div className={`${styles.messageContent} ${isOnRightSide ? styles.right : ''}`}>
            <img src={arrowAssistant} className={styles.arrowIcon} />
            <img src={bgAssistant} className={styles.bgIcon} />
            <div className={styles.messageText}>
              {notificationContent}
            </div>
          </div>
        )}
      </div>
    </Draggable>
  )
}

export default FloatingWidget
