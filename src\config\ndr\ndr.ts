import Constant from '@/config/constant'
import network from './network'

export default async function createNdrInstanceCourseware() {
  const env = Constant.NDR.ENV
  const params = {
    tenant_id: Constant.TENANT_ID,
    container_id: Constant.AI_COURSEWARE_CONTAINER_ID
  }
  const { access_token: token } = await network.getNDRAccessToken(params)

  NDR.setGlobalEnv(env)
  const ndr = new NDR({ env, ...params })
  ndr.setAccessToken(token)
  window.ndrInstance = ndr
}
