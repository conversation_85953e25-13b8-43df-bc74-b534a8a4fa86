import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useState
} from 'react'
import clueDefault from '@/asset/images/animation/clue/clue_default.png'

import store, { useAppDispatch, useAppSelector } from '@/store'
import { updateData } from '@/page/earth/earthSlice'
import _ from 'lodash'
import { v4 as uuidv4 } from 'uuid'
import EventBus from '@/component/EventBus'
import { Events } from '@/component/EventBus/constant'
import { sleep } from 'cloud-office-util/help'
import { getElementPosition } from '@/util/box'
import useScreenshot from './useScreenshot'
import useTransitionAnimation from './useTransitionAnimation'
import styles from './ScreenshotCard.module.scss'

interface IScreenshotMessageProps {
  img_key: string
  title?: string
  description?: string
  content?: string
  img_url: string
}

const ScreenshotCard = forwardRef<any, any>((__, ref) => {
  const {
    screenshotCardRef, captureScreenshot, waitForImagesToLoad
  } = useScreenshot()
  const [screenshotShow, setScreenshotShow] = useState(false)

  const {
    animateTo, resetElement
  } = useTransitionAnimation()

  const [message, setMessage] = React.useState<IScreenshotMessageProps>({} as unknown as IScreenshotMessageProps)

  const { data } = useAppSelector((state) => state.earth)
  const { books } = data

  const dispatch = useAppDispatch()
  const onChangeData = (newData) => {
    dispatch(updateData({
      books: {
        ...books,
        hypothesis: {
          ...(books.hypothesis || {}),
          ...newData
        }
      }
    }))
  }
  const getClueImg = ({ img_url }) => {
    if (img_url) return img_url
    return clueDefault
  }

  const capture = async (_message: IScreenshotMessageProps, callback?: () => void) => {
    setMessage(_message)
    const element = document.getElementById('screenshotCard')
    const duration = 1 // 动画持续时间
    // 执行截图并更新 clue
    const change = async (result) => {
      const clue = store.getState().earth.data.books.hypothesis.clue || []
      const newClues = _.cloneDeep(clue)
      newClues.push({
        id: _message.img_key || uuidv4(),
        title: _message.title || '与馆长对话',
        des: _message.description || '',
        img: result
      })
      onChangeData({
        clue: newClues
      })
      const position = getElementPosition(document.getElementById('book-btn')!)
      animateTo(element, {
        x: position.x,
        y: position.y - 30,
        scale: 0.2,
        opacity: 0.3,
        duration
      })
      await sleep(duration * 1000) // 等待动画完成
      setScreenshotShow(false)
      resetElement(element)
    }
    await waitForImagesToLoad(screenshotCardRef.current!)
    setScreenshotShow(true)
    if (_message.img_url) {
      change(_message.img_url)
    } else {
      captureScreenshot((result) => {
        change(result)
        callback?.()
      })
    }
  }
  useImperativeHandle(ref, () => ({
    capture,
  }), [])

  useEffect(() => {
    EventBus.on(Events.getClue, capture)
    return () => {
      EventBus.off(Events.getClue, capture)
    }
  }, [])

  return (
    <div
      className={styles.screenshotCard}
      ref={ref}
      style={{
        display: screenshotShow ? 'block' : 'none',
      }}
      id="screenshotCard"
    >
      <div ref={screenshotCardRef} className={styles.screenshotContent}>
        <img src={getClueImg(message)} />
        <span>{message?.content}</span>
      </div>
      <div className={styles.screenshotTitle}>
        {message?.title || '与馆长对话'}
      </div>
    </div>
  )
})

export default ScreenshotCard
