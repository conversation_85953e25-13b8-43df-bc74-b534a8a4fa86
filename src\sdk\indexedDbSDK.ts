import { PLAY_KEY } from '@/page/earth/constant'
import { DEFAULT_DATA } from '@/page/earth/earthSlice'
import Dexie from 'dexie'
import { v4 as uuidv4 } from 'uuid'

export default class IndexedDbSDK {
  private static instance: IndexedDbSDK

  publicDB: any

  db: any

  currentSaveId: null

  SAVE_META_TABLE: string

  constructor() {
    this.publicDB = null
    this.db = null
    this.currentSaveId = null // 当前选中的存档ID
    this.SAVE_META_TABLE = 'saves_meta' // 存档元数据表
  }

  /**
 * 获取单例实例
 * @param targetOrigin 父页面的目标 origin
 */
  public static getInstance(): IndexedDbSDK {
    if (!IndexedDbSDK.instance) {
      IndexedDbSDK.instance = new IndexedDbSDK()
    }
    return IndexedDbSDK.instance
  }

  // 初始化公共数据库（包含存档元数据）
  async initPublicDB() {
    this.publicDB = new Dexie('PublicGameDB')
    this.publicDB.version(1).stores({
      [this.SAVE_META_TABLE]: 'id, name, createdAt, lastPlayed'
    })
    await this.publicDB.open()
  }

  // 创建新存档
  async createSave(saveName) {
    // 生成UUID作为存档ID
    const saveId = uuidv4()
    // 在公共库中创建元数据
    await this.publicDB[this.SAVE_META_TABLE].add({
      id: saveId,
      name: saveName,
      createdAt: new Date().getTime(),
      lastPlayed: new Date().getTime()
    })
    // 初始化存档数据库
    this.db = new Dexie(`GameSave_${saveId}`)
    this.db.version(1).stores({
      PlayData: '++id, data',
    })
    await this.db.open()
    // 初始化默认一条数据
    this.db.PlayData.add({
      id: PLAY_KEY,
      data: DEFAULT_DATA,
    })
    return saveId
  }

  // 加载存档
  async loadSave(saveId) {
    // 关闭当前存档数据库（如果存在）
    if (this.db) this.db.close()
    // 更新最后游玩时间
    await this.publicDB[this.SAVE_META_TABLE].update(saveId, {
      lastPlayed: new Date().getTime()
    })
    // 初始化存档数据库
    this.db = new Dexie(`GameSave_${saveId}`)
    this.db.version(1).stores({
      PlayData: '++id, data',
    })
    await this.db.open()
    this.currentSaveId = saveId
  }

  // 删除存档
  async deleteSave(saveId) {
    // 删除元数据
    await this.publicDB[this.SAVE_META_TABLE].delete(saveId)

    // 删除存档数据库
    const dbName = `GameSave_${saveId}`
    await Dexie.delete(dbName)
  }

  // 获取所有存档
  async getAllSaves() {
    return this.publicDB[this.SAVE_META_TABLE].toArray()
  }

  // === 存档数据操作 ===

  async addPlayData(player) {
    return this.db.PlayData.add(player)
  }

  async getPlayData(id) {
    return this.db.PlayData.get(id)
  }

  async updatePlayData(id, updates) {
    this.publicDB[this.SAVE_META_TABLE].update(this.currentSaveId, {
      lastPlayed: new Date().getTime()
    })
    return this.db.PlayData.update(id, updates)
  }

  // ... 其他存档相关操作方法
}
