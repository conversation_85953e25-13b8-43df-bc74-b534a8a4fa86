import URI from 'urijs'

// 获取查询参数
export function getQuery(): Record<string, string>
export function getQuery(key: string): string | undefined
export function getQuery(key?: string): string | undefined | Record<string, string> {
  const uri = new URI()
  const search = uri.search(true)

  return key ? search[key] : search
}

// 获取哈希查询参数
export function getHashQuery(): Record<string, string>
export function getHashQuery(key: string): string | undefined
export function getHashQuery(key?: string): string | undefined | Record<string, string> {
  const uri = new URI()
  const fragment = uri.fragment()
  const hashSearch = new URI(fragment).search(true)

  return key ? hashSearch[key] : hashSearch
}

export const getAllQueries = (): Record<string, string> => ({
  ...getQuery(),
  ...getHashQuery()
})
